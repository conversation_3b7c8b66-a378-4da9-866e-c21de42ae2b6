"use client";
import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { Card } from "@/components/ui/Card";
import { toast } from "react-toastify";
import Link from "next/link";
import departmentApiRequest from "@/apiRequests/department";
import positionApiRequest, { Position } from "@/apiRequests/position";
import {
  ArrowLeft,
  Save,
  Upload,
  Plus,
  X,
  User,
  Shield,
  Award
} from "react-feather";
import { getLeaderImageUrl } from "@/utils/imageUtils";

interface LeaderFormData {
  name: string;
  position: string;
  department: string;
  bio?: string;
  experience?: string;
  education?: string;
  achievements: string[];
  phone: string;
  email: string;
  image: string;
  order: number;
  isActive: boolean;
}

interface Department {
  id: string;
  name: string;
}

const EditLeader = () => {
  const router = useRouter();
  const params = useParams();
  const leaderId = params.id as string;
    const [sessionToken, setSessionToken] = useState<string>("");
  const [loading, setLoading] = useState(false);
  const [pageLoading, setPageLoading] = useState(true);
  const [imageUploading, setImageUploading] = useState(false);
  const [newAchievement, setNewAchievement] = useState("");
  const [departments, setDepartments] = useState<Department[]>([]);
  const [positions, setPositions] = useState<Position[]>([]);
  const [loadingPositions, setLoadingPositions] = useState(false);

  const [formData, setFormData] = useState<LeaderFormData>({
    name: "",
    position: "",
    department: "",
    bio: "",
    experience: "",
    education: "",
    achievements: [],
    phone: "",
    email: "",
    image: "",
    order: 1,
    isActive: true
  });

  const [errors, setErrors] = useState<Partial<LeaderFormData>>({});

  useEffect(() => {
    const token = localStorage.getItem("sessionToken") || "";
    setSessionToken(token);
  }, []);
  useEffect(() => {
    if (!sessionToken || !leaderId) return;
    fetchLeaderData();
    fetchDepartments();
  }, [sessionToken, leaderId]);
  const fetchDepartments = async () => {
    try {
      // Use the departments API instead of extracting from leaders
      console.log("Fetching departments for leader edit form...");
      const result = await departmentApiRequest.getDepartments(sessionToken);
      
      if (result.payload.success) {
        console.log("Fetched departments for leader edit form:", result.payload.data);
        setDepartments(result.payload.data || []);
      } else {
        console.error("Error fetching departments:", result.payload.message);
        // Fallback to extracting from leaders if departments API fails
        await fetchDepartmentsFromLeaders();
      }
    } catch (error) {
      console.error("Error fetching departments:", error);
      // Fallback to extracting from leaders if departments API fails
      await fetchDepartmentsFromLeaders();
    }
  };

  const fetchDepartmentsFromLeaders = async () => {
    try {
      // Fallback: Get leaders first to extract departments
      const response = await fetch("/api/admin/leaders", {
        headers: {
          "Authorization": `Bearer ${sessionToken}`,
        },
      });
      const result = await response.json();
      
      if (response.ok) {
        const leaders = result.data || [];
        const uniqueDepartments = [...new Set(leaders.map((l: any) => l.department))].filter(Boolean) as string[];
        const departmentList = uniqueDepartments.map((name, index) => ({
          id: index.toString(),
          name: name
        }));
        setDepartments(departmentList);
        console.log("Fetched departments from leaders (fallback):", departmentList);
      }
    } catch (error) {
      console.error("Error fetching departments from leaders:", error);
    }
  };

  // Fetch positions for selected department
  const fetchPositions = async (departmentId: string) => {
    if (!departmentId || !sessionToken) {
      setPositions([]);
      return;
    }

    try {
      setLoadingPositions(true);
      const result = await positionApiRequest.getPositionsByDepartment(departmentId, sessionToken);

      if (result.payload.success) {
        setPositions(result.payload.data || []);
      } else {
        console.error("Error fetching positions:", result.payload.message);
        setPositions([]);
        toast.error("Không thể tải danh sách chức vụ");
      }
    } catch (error) {
      console.error("Error fetching positions:", error);
      setPositions([]);
      toast.error("Lỗi khi tải danh sách chức vụ");
    } finally {
      setLoadingPositions(false);
    }
  };

  // Handle department change
  const handleDepartmentChange = (departmentId: string) => {
    setFormData(prev => ({
      ...prev,
      department: departmentId,
      position: "" // Reset position when department changes
    }));

    // Fetch positions for the selected department
    if (departmentId) {
      fetchPositions(departmentId);
    } else {
      setPositions([]);
    }
  };
  const fetchLeaderData = async () => {
    try {
      setPageLoading(true);
      
      const response = await fetch(`/api/admin/leaders/${leaderId}`, {
        headers: {
          "Authorization": `Bearer ${sessionToken}`,
        },
      });
      const result = await response.json();
      
      if (response.ok && result.data) {
        const leader = result.data;
        setFormData({
          name: leader.name || "",
          position: leader.position || "",
          department: leader.department || "",
          bio: leader.bio || "",
          experience: leader.experience || "",
          education: leader.education || "",
          achievements: leader.achievements || [],
          phone: leader.phone || "",
          email: leader.email || "",
          image: leader.image || "",
          order: leader.order || 1,
          isActive: leader.isActive !== undefined ? leader.isActive : true
        });
      } else {
        throw new Error(result.message || "Không tìm thấy thông tin lãnh đạo");
      }
      
    } catch (error) {
      console.error("Error fetching leader:", error);
      toast.error("Lỗi khi tải thông tin lãnh đạo");
      router.push("/dashboard/secret/leaders");
    } finally {
      setPageLoading(false);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<LeaderFormData> = {};

    if (!formData.name.trim()) {
      newErrors.name = "Vui lòng nhập họ tên";
    }

    if (!formData.position.trim()) {
      newErrors.position = "Vui lòng chọn chức vụ";
    }

    if (!formData.department.trim()) {
      newErrors.department = "Vui lòng chọn phòng ban";
    }

    // bio, experience, education are now optional - no validation needed

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Email không đúng định dạng";
    }

    if (formData.phone && !/^[0-9]{10,11}$/.test(formData.phone.replace(/\s+/g, ""))) {
      newErrors.phone = "Số điện thoại không đúng định dạng";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof LeaderFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const addAchievement = () => {
    if (newAchievement.trim()) {
      setFormData(prev => ({
        ...prev,
        achievements: [...prev.achievements, newAchievement.trim()]
      }));
      setNewAchievement("");
    }
  };

  const removeAchievement = (index: number) => {
    setFormData(prev => ({
      ...prev,
      achievements: prev.achievements.filter((_, i) => i !== index)
    }));
  };

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith("image/")) {
      toast.error("Vui lòng chọn file hình ảnh");
      return;
    }

    // Validate file size (5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error("Kích thước file không được vượt quá 5MB");
      return;
    }

    try {
      setImageUploading(true);

      const formData = new FormData();
      formData.append("file", file);

      const response = await fetch("/api/admin/leaders/upload-image", {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${sessionToken}`,
        },
        body: formData,
      });
      const result = await response.json();

      if (response.ok) {
        setFormData(prev => ({ ...prev, image: result.url }));
        toast.success("Đã tải ảnh lên thành công");
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error("Error uploading image:", error);
      toast.error("Lỗi khi tải ảnh lên");
    } finally {
      setImageUploading(false);
    }
  };
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast.error("Vui lòng kiểm tra lại thông tin");
      return;
    }

    try {
      setLoading(true);
      
      const response = await fetch(`/api/admin/leaders/${leaderId}`, {
        method: "PUT",
        headers: {
          "Authorization": `Bearer ${sessionToken}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });
      const result = await response.json();
      
      if (response.ok) {
        toast.success("Đã cập nhật thông tin lãnh đạo thành công");
        router.push("/dashboard/secret/leaders");
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error("Error updating leader:", error);
      toast.error("Lỗi khi cập nhật thông tin lãnh đạo");
    } finally {
      setLoading(false);
    }
  };

  const getPositionIcon = (position: string) => {
    if (position.includes("Chánh án")) return <Shield size={20} className="text-red-600" />;
    if (position.includes("Phó")) return <Award size={20} className="text-blue-600" />;
    return <User size={20} className="text-gray-600" />;
  };

  if (pageLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <div className="w-8 h-8 bg-gray-200 rounded-lg animate-pulse"></div>
          <div className="space-y-2">
            <div className="h-6 bg-gray-200 rounded w-48 animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded w-64 animate-pulse"></div>
          </div>
        </div>
        {Array.from({ length: 3 }).map((_, index) => (
          <Card key={index}>
            <div className="animate-pulse">
              <div className="h-6 bg-gray-200 rounded w-1/4 mb-6"></div>
              <div className="space-y-4">
                <div className="h-12 bg-gray-200 rounded"></div>
                <div className="h-12 bg-gray-200 rounded"></div>
                <div className="h-32 bg-gray-200 rounded"></div>
              </div>
            </div>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link
            href="/dashboard/secret/leaders"
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft size={20} />
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Chỉnh sửa thông tin lãnh đạo
            </h1>
            <p className="text-gray-600 mt-2">
              Cập nhật thông tin cho {formData.name}
            </p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <h2 className="text-xl font-semibold text-gray-900 mb-6">
            Thông tin cơ bản
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Họ và tên *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.name ? "border-red-500" : "border-gray-300"
                }`}
                placeholder="Nhập họ và tên"
              />
              {errors.name && (
                <p className="text-red-500 text-sm mt-1">{errors.name}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Chức vụ *
              </label>
              <div className="relative">
                <select
                  value={formData.position}
                  onChange={(e) => handleInputChange("position", e.target.value)}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none ${
                    errors.position ? "border-red-500" : "border-gray-300"
                  }`}
                >
                  <option value="">Chọn chức vụ</option>
                  {positionOptions.map((position) => (
                    <option key={position} value={position}>
                      {position}
                    </option>
                  ))}
                </select>
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  {formData.position && getPositionIcon(formData.position)}
                </div>
              </div>
              {errors.position && (
                <p className="text-red-500 text-sm mt-1">{errors.position}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Phòng ban *
              </label>
              <select
                value={formData.department}
                onChange={(e) => handleDepartmentChange(e.target.value)}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.department ? "border-red-500" : "border-gray-300"
                }`}
              >
                <option value="">Chọn phòng ban</option>
                {departments.map((dept) => (
                  <option key={dept.id} value={dept.id}>
                    {dept.name}
                  </option>
                ))}
              </select>
              {errors.department && (
                <p className="text-red-500 text-sm mt-1">{errors.department}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Thứ tự hiển thị
              </label>
              <input
                type="number"
                min="1"
                value={formData.order}
                onChange={(e) => handleInputChange("order", parseInt(e.target.value) || 1)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="1"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Số điện thoại
              </label>
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => handleInputChange("phone", e.target.value)}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.phone ? "border-red-500" : "border-gray-300"
                }`}
                placeholder="0123456789"
              />
              {errors.phone && (
                <p className="text-red-500 text-sm mt-1">{errors.phone}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email
              </label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.email ? "border-red-500" : "border-gray-300"
                }`}
                placeholder="<EMAIL>"
              />
              {errors.email && (
                <p className="text-red-500 text-sm mt-1">{errors.email}</p>
              )}
            </div>
          </div>
        </Card>

        {/* Professional Information */}
        <Card>
          <h2 className="text-xl font-semibold text-gray-900 mb-6">
            Thông tin nghề nghiệp
          </h2>
          
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Kinh nghiệm
              </label>
              <input
                type="text"
                value={formData.experience || ""}
                onChange={(e) => handleInputChange("experience", e.target.value)}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.experience ? "border-red-500" : "border-gray-300"
                }`}
                placeholder="25 năm"
              />
              {errors.experience && (
                <p className="text-red-500 text-sm mt-1">{errors.experience}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Trình độ học vấn
              </label>
              <input
                type="text"
                value={formData.education || ""}
                onChange={(e) => handleInputChange("education", e.target.value)}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.education ? "border-red-500" : "border-gray-300"
                }`}
                placeholder="Thạc sĩ Luật, Đại học Luật Hà Nội"
              />
              {errors.education && (
                <p className="text-red-500 text-sm mt-1">{errors.education}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tiểu sử
              </label>
              <textarea
                value={formData.bio || ""}
                onChange={(e) => handleInputChange("bio", e.target.value)}
                rows={6}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.bio ? "border-red-500" : "border-gray-300"
                }`}
                placeholder="Mô tả chi tiết về quá trình công tác, kinh nghiệm và thành tựu..."
              />
              {errors.bio && (
                <p className="text-red-500 text-sm mt-1">{errors.bio}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Thành tích & Giải thưởng
              </label>
              <div className="space-y-3">
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={newAchievement}
                    onChange={(e) => setNewAchievement(e.target.value)}
                    className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Nhập thành tích..."
                    onKeyPress={(e) => e.key === "Enter" && addAchievement()}
                  />
                  <button
                    type="button"
                    onClick={addAchievement}
                    className="px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center"
                  >
                    <Plus size={20} />
                  </button>
                </div>
                {formData.achievements.length > 0 && (
                  <div className="space-y-2">
                    {formData.achievements.map((achievement, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between bg-gray-50 px-4 py-2 rounded-lg"
                      >
                        <span className="text-gray-700">{achievement}</span>
                        <button
                          type="button"
                          onClick={() => removeAchievement(index)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <X size={16} />
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </Card>

        {/* Image Upload */}
        <Card>
          <h2 className="text-xl font-semibold text-gray-900 mb-6">
            Ảnh đại diện
          </h2>
          
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <div className="w-24 h-24 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden">
                {formData.image ? (
                  <img
                    src={getLeaderImageUrl(formData.image)}
                    alt="Preview"
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <User size={32} className="text-gray-400" />
                )}
              </div>
              <div>
                <label className="cursor-pointer bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg inline-flex items-center space-x-2">
                  <Upload size={20} />
                  <span>{imageUploading ? "Đang tải..." : "Chọn ảnh mới"}</span>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                    disabled={imageUploading}
                  />
                </label>
                <p className="text-sm text-gray-500 mt-2">
                  Chọn ảnh JPG, PNG. Tối đa 5MB.
                </p>
              </div>
            </div>
          </div>
        </Card>

        {/* Settings */}
        <Card>
          <h2 className="text-xl font-semibold text-gray-900 mb-6">
            Cài đặt
          </h2>
          
          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="isActive"
              checked={formData.isActive}
              onChange={(e) => handleInputChange("isActive", e.target.checked)}
              className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
            />
            <label htmlFor="isActive" className="text-gray-700">
              Hiển thị trên trang web
            </label>
          </div>
        </Card>

        {/* Submit Buttons */}
        <div className="flex items-center justify-end space-x-4">
          <Link
            href="/dashboard/secret/leaders"
            className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            Hủy
          </Link>
          <button
            type="submit"
            disabled={loading}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg flex items-center space-x-2 transition-colors disabled:opacity-50"
          >
            <Save size={20} />
            <span>{loading ? "Đang lưu..." : "Cập nhật thông tin"}</span>
          </button>
        </div>
      </form>
    </div>
  );
};

export default EditLeader;
