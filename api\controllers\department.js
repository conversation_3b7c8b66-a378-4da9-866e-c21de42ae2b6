const Leader = require("../models/leader");
const Department = require("../models/department");

// Get all departments (from both Department model and leaders)
const getAllDepartments = async (req, res) => {
  try {
    // Get departments from Department model
    const storedDepartments = await Department.find({}).sort({ createdAt: 1 });
    
    // Get unique departments from leaders (for backward compatibility)
    const leaderDepartments = await Leader.distinct('department');
    
    // Combine and deduplicate departments
    const allDepartmentNames = new Set();
    
    // Add stored departments
    storedDepartments.forEach(dept => {
      allDepartmentNames.add(dept.name);
    });
    
    // Add leader departments (for any that might not be in Department model)
    leaderDepartments.forEach(dept => {
      if (dept && dept.trim()) {
        allDepartmentNames.add(dept.trim());
      }
    });
    
    // Create final department list
    const departmentList = Array.from(allDepartmentNames).map((name, index) => {
      const storedDept = storedDepartments.find(dept => dept.name === name);
      return {
        id: storedDept ? storedDept._id.toString() : index.toString(),
        name: name
      };
    });

    res.status(200).json({
      success: true,
      data: departmentList,
      message: "Lấy danh sách phòng ban thành công"
    });
  } catch (error) {
    console.error("Error in getAllDepartments:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi server khi lấy danh sách phòng ban"
    });
  }
};

// Create new department (store in Department model)
const createDepartment = async (req, res) => {
  try {
    const { name } = req.body;

    if (!name || !name.trim()) {
      return res.status(400).json({
        success: false,
        message: "Tên phòng ban không được để trống"
      });
    }

    // Check if department already exists in Department model
    const existingDepartment = await Department.findOne({ name: name.trim() });
    if (existingDepartment) {
      return res.status(409).json({
        success: false,
        message: "Phòng ban đã tồn tại"
      });
    }

    // Check if department exists in leaders (for backward compatibility)
    const existingInLeaders = await Leader.findOne({ department: name.trim() });
    if (existingInLeaders) {
      return res.status(409).json({
        success: false,
        message: "Phòng ban đã tồn tại"
      });
    }

    // Create new department
    const newDepartment = new Department({
      name: name.trim()
    });

    const savedDepartment = await newDepartment.save();

    res.status(201).json({
      success: true,
      data: { 
        id: savedDepartment._id.toString(), 
        name: savedDepartment.name 
      },
      message: "Phòng ban được tạo thành công"
    });
  } catch (error) {
    console.error("Error in createDepartment:", error);
    if (error.code === 11000) {
      return res.status(409).json({
        success: false,
        message: "Phòng ban đã tồn tại"
      });
    }
    res.status(500).json({
      success: false,
      message: "Lỗi server khi tạo phòng ban"
    });
  }
};

// Update department name (update both Department model and all leaders)
const updateDepartment = async (req, res) => {
  try {
    const { id } = req.params;
    const { name } = req.body;

    if (!name || !name.trim()) {
      return res.status(400).json({
        success: false,
        message: "Tên phòng ban không được để trống"
      });
    }

    // Try to find department in Department model first
    const existingDepartment = await Department.findById(id);
    let oldName = null;

    if (existingDepartment) {
      oldName = existingDepartment.name;
    } else {
      // Fallback: try to find by index in leaders (for backward compatibility)
      const departments = await Leader.distinct('department');
      oldName = departments[parseInt(id)] || departments.find(d => d && d.includes(name));
    }

    if (!oldName) {
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy phòng ban"
      });
    }

    // Check if new name already exists (and different from old name)
    if (oldName !== name.trim()) {
      const existingByName = await Department.findOne({ name: name.trim() });
      const existingInLeaders = await Leader.findOne({ department: name.trim() });
      
      if (existingByName || existingInLeaders) {
        return res.status(409).json({
          success: false,
          message: "Tên phòng ban đã tồn tại"
        });
      }
    }

    // Update department in Department model if it exists
    if (existingDepartment) {
      existingDepartment.name = name.trim();
      await existingDepartment.save();
    }

    // Update all leaders with the old department name
    await Leader.updateMany(
      { department: oldName },
      { department: name.trim() }
    );

    res.status(200).json({
      success: true,
      data: { id, name: name.trim() },
      message: "Cập nhật phòng ban thành công"
    });
  } catch (error) {
    console.error("Error in updateDepartment:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi server khi cập nhật phòng ban"
    });
  }
};

// Delete department (only if no leaders belong to it)
const deleteDepartment = async (req, res) => {
  try {
    const { id } = req.params;

    // Try to find department in Department model first
    const existingDepartment = await Department.findById(id);
    let departmentName = null;

    if (existingDepartment) {
      departmentName = existingDepartment.name;
    } else {
      // Fallback: try to find by index in leaders (for backward compatibility)
      const departments = await Leader.distinct('department');
      departmentName = departments[parseInt(id)] || departments.find(d => d);
    }

    if (!departmentName) {
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy phòng ban"
      });
    }

    // Check if any leaders belong to this department
    const leadersCount = await Leader.countDocuments({ department: departmentName });
    if (leadersCount > 0) {
      return res.status(400).json({
        success: false,
        message: `Không thể xóa! Còn ${leadersCount} lãnh đạo thuộc phòng ban này`
      });
    }

    // Delete from Department model if it exists
    if (existingDepartment) {
      await Department.findByIdAndDelete(id);
    }

    res.status(200).json({
      success: true,
      message: "Xóa phòng ban thành công"
    });
  } catch (error) {
    console.error("Error in deleteDepartment:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi server khi xóa phòng ban"
    });
  }
};

module.exports = {
  getAllDepartments,
  createDepartment,
  updateDepartment,
  deleteDepartment
};
