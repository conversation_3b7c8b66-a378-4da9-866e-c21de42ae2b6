const mongoose = require("mongoose");
const User = require("../models/user");
const Post = require("../models/post");
const Page = require("../models/page");
const { PageView, DailyAnalytics } = require("../models/analytics");
// const Payment = require("../models/payment");
var moment = require("moment");
const defineAbilityFor = require("../permissions/abilities");
const { ForbiddenError } = require("@casl/ability");
const bcrypt = require("bcryptjs");
const { customAlphabet } = require("nanoid");
const UserLog = require("../models/UserLog");
const { validatePassword, getPasswordErrorMessage } = require("../utils/passwordValidator");

exports.userSignup = async (req, res) => {
 
  try {
    const { password, username, phonenumber } = req.body;
    const email = req.body.email?.toLowerCase(); // Ensure email is defined before calling `toLowerCase()`

    if (!email || !password || !username) {
      return res.status(403).json({
        success: false,
        message: "<PERSON>ui lòng điền đầy đủ thông tin.",
      });
    }

    // Validate password
    if (!validatePassword(password)) {
      return res.status(403).json({
        success: false,
        message: getPasswordErrorMessage(),
      });
    }

    // Check if phone number exists
    if (phonenumber) {
      const existingUserPhone = await User.findOne({ phonenumber });
      if (existingUserPhone) {
        return res.status(403).json({
          success: false,
          message: "Số điện thoại đã được sử dụng.",
          userErr: true,
          mailErr: false,
        });
      }
    }

    // Check if email exists
    const existingUserEmail = await User.findOne({ email });
    if (existingUserEmail) {
      return res.status(403).json({
        success: false,
        message: "Địa chỉ email này đã được sử dụng.",
        userErr: false,
        mailErr: true,
      });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Generate user code
    const nanoid = customAlphabet("1234567890abcdef", 8);
    const user = new User({
      username,
      email,
      password: hashedPassword,
      code: nanoid(),
      ...(phonenumber && { phonenumber }), // Only include `phonenumber` if it's provided
    });

    await user.save();

    res.status(201).json({
      success: true,
      message: "Đã tạo tài khoản mới thành công.",
      user,
    });
  } catch (err) {
    console.error("Signup Error:", err);
    res.status(500).json({
      success: false,
      message: "Đã xảy ra lỗi, vui lòng thử lại sau.",
      error: err.message,
    });
  }
};


exports.get_all_user = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  const { page, perPage } = req.body;
  // let type = "desc";
  // const field = sort.field;
  // if (sort.type !== "none") {
  //   type = sort.type;
  // }
  try {
    let onTotal = User.countDocuments();
    let onUsers = User.find()
      .select("_id username phonenumber email rule gender private createdAt")
      .sort({ createdAt: "desc" })
      .skip(perPage * page - perPage) // Trong page đầu tiên sẽ bỏ qua giá trị là 0
      .limit(perPage);
    let [total, users] = await Promise.all([onTotal, onUsers]);
    ForbiddenError.from(ability).throwUnlessCan("read", users);
    res.json({
      success: true,
      users,
      total,
    });
  } catch (err) {
    res.json({
      success: false,
      message: "something wrong",
    });
  }
};

exports.search_user = async (req, res) => {
  let email = { $regex: new RegExp("^" + req.body.email.toLowerCase()) };
  try {
    let users = await User.find({ email })
      .sort({ createdAt: "desc" })
      .limit(20);
    res.json({
      success: true,
      users,
    });
  } catch (err) {
    res.status(500).json({
      message: "Please check correctness and try again",
      error: err,
      success: false,
    });
  }
};

exports.get_single_user = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  let _id = req.params.id;
  console.log("Fetching user with ID:", _id);
  console.log("Request user:", req.user?.username, req.user?.rule);

  try {
    var user = await User.findById({ _id })
      .sort({ createdAt: "desc" })
      .select(
        "username email rule Wallet private address phonenumber private rank bio gender categories"
      )
      .exec();

    console.log("Found user:", user ? user.username : "null");

    if (!user) {
      return res.status(404).json({
        status: false,
        success: false,
        message: "User not found"
      });
    }

    ForbiddenError.from(ability).throwUnlessCan("read", user);
    console.log("User access granted");
    res.status(200).json({ status: true, user, success: true });
  } catch (err) {
    console.error("Error in get_single_user:", err);
    if (err.name === 'ForbiddenError') {
      res.status(403).json({
        status: false,
        success: false,
        message: "Access denied"
      });
    } else if (err.name === 'CastError') {
      res.status(400).json({
        status: false,
        success: false,
        message: "Invalid user ID format"
      });
    } else {
      res.status(500).json({
        status: false,
        success: false,
        message: "Internal server error"
      });
    }
  }
};

exports.delete_user = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  let _id = req.params.id;
  try {
    var user = await User.findById({ _id }).exec();
    ForbiddenError.from(ability).throwUnlessCan("delete", user);
    await User.findByIdAndDelete(_id);
    res.status(200).json({ success: true });
  } catch (err) {
    res.status(500).send(err);
  }
};

exports.admin_change_password = async (req, res, next) => {
  const ability = defineAbilityFor(req.user);
  const _id = req.body._id;
  const newPassword = req.body.password;
  try {
    let foundUser = await User.findById({ _id });
    if (!validatePassword(newPassword)) {
      return res.status(403).json({
        success: false,
        message: getPasswordErrorMessage(),
      });
    }
    ForbiddenError.from(ability).throwUnlessCan("update", foundUser);

    bcrypt.hash(newPassword, 10, async (err, hash) => {
      if (err) {
        return res.status(500).json({
          error: err,
          message: "Something wrong",
        });
      } else {
        foundUser.password = hash;
        await foundUser.save();
        // const html = PassChanged;
        // // Send email
        // mailer.sendEmail(
        //   "<EMAIL>",
        //   foundUser.email,
        //   "Password have been change by Administartor!",
        //   html
        // );
        res.status(200).json({
          success: true,
          message: "Password has been change",
        });
      }
    });
  } catch (err) {
    res.json(500).status({
      error: err,
      success: false,
      message: "Server Error",
    });
  }
};

exports.add_deposit = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  const nanoid = customAlphabet("1234567890abcdef", 6);
  let { amount, buyer } = req.body;
  try {
    let method = await Payment.findOne({});
    let deposit = new Deposit({
      code: nanoid(),
      amount: amount,
      buyer,
      method: method._id,
      paymentAmount: amount,
      status: "Completed",
    });
    ForbiddenError.from(ability).throwUnlessCan("create", deposit);
    await deposit.save();
    builUserBalance(buyer);
    res.status(200).json({
      success: true,
      deposit,
    });
  } catch (err) {
    res.status(500).json({
      message: err,
      success: false,
    });
  }
};

exports.get_deposit_by_date = async (req, res, next) => {
  let rule = req.user.rule;
  let ability = defineAbilityFor(req.user);
  let perPage = 20; // số lượng sản phẩm xuất hiện trên 1 page
  let page = parseInt(req.query.page) || 1;
  let { start, end } = req.body;
  let startFormat = moment.utc(String(start)).toDate();
  let endFormat = moment.utc(String(end)).add(24, "hours").toDate();
  try {
    if (rule == "admin") {
      let getTotal = Deposit.countDocuments({
        updatedAt: {
          $gte: startFormat,
          $lte: endFormat,
        },
      });
      let getDeposits = Deposit.find({
        updatedAt: {
          $gte: startFormat,
          $lte: endFormat,
        },
      })
        .populate("method", "methodname")
        .populate("buyer", "email")
        .sort({ updatedAt: "desc" })
        .skip(perPage * page - perPage) // Trong page đầu tiên sẽ bỏ qua giá trị là 0
        .limit(perPage);

      let getResult = Deposit.aggregate([
        {
          $match: {
            $and: [
              { updatedAt: { $gte: startFormat } },
              { updatedAt: { $lte: endFormat } },
            ],
          },
        },
        {
          $group: {
            _id: "$status",
            total: { $sum: { $multiply: ["$amount"] } },
          },
        },
      ]);
      let [total, deposits, result] = await Promise.all([
        getTotal,
        getDeposits,
        getResult,
      ]);
      ForbiddenError.from(ability).throwUnlessCan("read", deposits);
      let completed = result.find(({ _id }) => _id === "Completed") || {
        total: 0,
      };
      let onHold = result.find(({ _id }) => _id === "On hold") || { total: 0 };
      let refunded = result.find(({ _id }) => _id === "Refunded") || {
        total: 0,
      };
      let canceled = result.find(({ _id }) => _id === "Canceled") || {
        total: 0,
      };
      let confirmed = result.find(({ _id }) => _id === "Confirmed") || {
        total: 0,
      };
      let status = {
        completed,
        onHold,
        canceled,
        refunded,
        confirmed,
      };
      res.status(200).json({
        total,
        deposits,
        success: true,
        status,
      });
    } else {
      res.status(500).json({
        success: false,
        mesage: "You can't accept this action",
      });
    }
  } catch (err) {
    res.status(500).json({
      error: err,
    });
  }
};

exports.get_withdraw_by_date = async (req, res, next) => {
  let rule = req.user.rule;
  let ability = defineAbilityFor(req.user);
  let perPage = 20; // số lượng sản phẩm xuất hiện trên 1 page
  let page = parseInt(req.query.page) || 1;
  let { start, end } = req.body;
  let startFormat = moment.utc(String(start)).toDate();
  let endFormat = moment.utc(String(end)).add(24, "hours").toDate();
  try {
    if (rule == "admin") {
      let getTotal = Withdraw.countDocuments({
        updatedAt: {
          $gte: startFormat,
          $lte: endFormat,
        },
      });
      let getResult = Withdraw.aggregate([
        {
          $match: {
            $and: [
              { updatedAt: { $gte: startFormat } },
              { updatedAt: { $lte: endFormat } },
            ],
          },
        },
        {
          $group: {
            _id: "$status",
            total: { $sum: { $multiply: ["$amount"] } },
          },
        },
      ]);
      let getWithdraws = Withdraw.find({
        updatedAt: {
          $gte: startFormat,
          $lte: endFormat,
        },
      })
        .populate("method", "methodname")
        .populate("buyer", "email")
        .sort({ updatedAt: "desc" })
        .skip(perPage * page - perPage) // Trong page đầu tiên sẽ bỏ qua giá trị là 0
        .limit(perPage);
      let [total, withdraws, result] = await Promise.all([
        getTotal,
        getWithdraws,
        getResult,
      ]);
      ForbiddenError.from(ability).throwUnlessCan("read", withdraws);
      let completed = result.find(({ _id }) => _id === "Completed") || {
        total: 0,
      };
      let onHold = result.find(({ _id }) => _id === "On hold") || { total: 0 };
      let confirmed = result.find(({ _id }) => _id === "Confirmed") || {
        total: 0,
      };
      let canceled = result.find(({ _id }) => _id === "Canceled") || {
        total: 0,
      };
      let refunded = result.find(({ _id }) => _id === "Refunded") || {
        total: 0,
      };
      let status = {
        completed,
        onHold,
        confirmed,
        canceled,
        refunded,
      };
      res.status(200).json({
        total,
        withdraws,
        success: true,
        status,
      });
    } else {
      res.status(500).json({
        success: false,
        mesage: "You can't accept this action",
      });
    }
  } catch (err) {
    res.status(500).json({
      error: err,
    });
  }
};

exports.get_partner = async (req, res, next) => {
  const ability = defineAbilityFor(req.user);
  try {
    let onGetBalance = User.aggregate([
      {
        $group: {
          _id: null,
          deposit: { $sum: { $multiply: ["$Wallet.deposit"] } },
          interest: { $sum: { $multiply: ["$Wallet.interest"] } },
          widthdrawAll: { $sum: { $multiply: ["$Wallet.withdraw"] } },
          widthdrawInterest: {
            $sum: { $multiply: ["$Wallet.withdrawInterest"] },
          },
        },
      },
    ]);
    let [balance] = await Promise.all([onGetBalance]);
    ForbiddenError.from(ability).throwUnlessCan("read", balance);
    let allPartnerData = {
      partnerDeposit: 0,
      partnerInterest: 0,
      partnerWidthdrawAll: 0,
      partnerWidthdrawInterest: 0,
      partnerRevenue: 0,
    };
    if (balance.length > 0) {
      (allPartnerData.partnerDeposit = balance[0].deposit),
        (allPartnerData.partnerInterest = balance[0].interest),
        (allPartnerData.partnerWidthdrawAll = balance[0].widthdrawAll),
        (allPartnerData.partnerWidthdrawInterest =
          balance[0].widthdrawInterest),
        (allPartnerData.partnerRevenue =
          allPartnerData.partnerDeposit - allPartnerData.partnerWidthdrawAll);
    }
    res.json({
      success: true,
      allPartnerData,
    });
  } catch (err) {
    res.status(500).json({ error: err });
  }
};

exports.get_partner_by_date = async (req, res, next) => {
  let rule = req.user.rule;
  let ability = defineAbilityFor(req.user);
  let { start, end } = req.body;
  let startFormat = moment.utc(String(start)).toDate();
  let endFormat = moment.utc(String(end)).add(24, "hours").toDate();
  try {
    if (rule == "admin") {
      let onWithdrawAll = Withdraw.aggregate([
        {
          $match: {
            $and: [
              { updatedAt: { $gte: startFormat } },
              { updatedAt: { $lte: endFormat } },
              { type: "deposit" },
              { status: "Completed" },
            ],
          },
        },
        {
          $group: {
            _id: "$status",
            total: { $sum: { $multiply: ["$amount"] } },
          },
        },
      ]);
      let onWithdrawInterest = Withdraw.aggregate([
        {
          $match: {
            $and: [
              { updatedAt: { $gte: startFormat } },
              { updatedAt: { $lte: endFormat } },
              { type: "interest" },
              { status: "Completed" },
            ],
          },
        },
        {
          $group: {
            _id: "$status",
            total: { $sum: { $multiply: ["$amount"] } },
          },
        },
      ]);
      let onDeposit = Deposit.aggregate([
        {
          $match: {
            $and: [
              { updatedAt: { $gte: startFormat } },
              { updatedAt: { $lte: endFormat } },
            ],
          },
        },
        {
          $group: {
            _id: "$status",
            total: { $sum: { $multiply: ["$amount"] } },
          },
        },
      ]);
      let onInterest = Interest.aggregate([
        {
          $match: {
            $and: [
              { updatedAt: { $gte: startFormat } },
              { updatedAt: { $lte: endFormat } },
            ],
          },
        },
        {
          $group: {
            _id: null,
            total: { $sum: { $multiply: ["$amount"] } },
          },
        },
      ]);
      let [getWithdrawAll, getWithdrawInterest, getDeposit, getInterest] =
        await Promise.all([
          onWithdrawAll,
          onWithdrawInterest,
          onDeposit,
          onInterest,
        ]);
      ForbiddenError.from(ability).throwUnlessCan("read", getWithdrawAll);
      let WithdrawAll = getWithdrawAll.find(
        ({ _id }) => _id === "Completed"
      ) || { total: 0 };
      let WithdrawInterest = getWithdrawInterest.find(
        ({ _id }) => _id === "Completed"
      ) || { total: 0 };
      let deposit = getDeposit.find(({ _id }) => _id === "Completed") || {
        total: 0,
      };
      let interest = 0;
      if (getInterest.length > 0) {
        interest = getInterest[0].total;
      }
      let data = {
        WithdrawAll,
        WithdrawInterest,
        deposit,
        interest,
      };
      res.status(200).json({
        success: true,
        data,
      });
    } else {
      res.status(500).json({
        success: false,
        mesage: "You can't accept this action",
      });
    }
  } catch (err) {
    res.status(500).json({
      error: err,
    });
  }
};

exports.refesh_user = async (req, res, next) => {
  try {
    let users = await User.find().select("Wallet").exec();
    for (user of users) {
      builUserBalance(user._id);
    }
    res.json({
      success: true,
    });
  } catch (err) {
    res.status(500).json({ error: err });
  }
};

exports.get_revenue = async (req, res, next) => {
  const ability = defineAbilityFor(req.user);
  let perPage = 20; // số lượng sản phẩm xuất hiện trên 1 page
  let page = parseInt(req.query.page) || 1;
  try {
    let onTotal = Revenue.countDocuments({});
    let onRevenue = Revenue.find({})
      .populate("host", "email")
      .populate("partner", "email")
      .populate("interest", "amount")
      .sort({ updatedAt: "desc" })
      .skip(perPage * page - perPage) // Trong page đầu tiên sẽ bỏ qua giá trị là 0
      .limit(perPage);
    let [total, revenue] = await Promise.all([onTotal, onRevenue]);
    ForbiddenError.from(ability).throwUnlessCan("read", revenue);
    res.json({
      success: true,
      total,
      revenue,
    });
  } catch (err) {
    res.status(500).json({ error: err });
  }
};

exports.delete_revenue = async (req, res, next) => {
  const ability = defineAbilityFor(req.user);
  let id = req.params.id;
  try {
    let onrevenue = await Revenue.findById({ _id: id });
    ForbiddenError.from(ability).throwUnlessCan("delete", onrevenue);
    await Revenue.findByIdAndDelete(id);
    res.json({
      success: true,
    });
  } catch (err) {
    res.status(500).json({ error: err });
  }
};

exports.get_interest = async (req, res, next) => {
  const ability = defineAbilityFor(req.user);
  let perPage = 20; // số lượng sản phẩm xuất hiện trên 1 page
  let page = parseInt(req.query.page) || 1;
  try {
    let onTotal = Interest.countDocuments({});
    let oninterest = Interest.find({})
      .populate("user", "email")
      .sort({ updatedAt: "desc" })
      .skip(perPage * page - perPage) // Trong page đầu tiên sẽ bỏ qua giá trị là 0
      .limit(perPage);
    let [total, interest] = await Promise.all([onTotal, oninterest]);
    ForbiddenError.from(ability).throwUnlessCan("read", interest);
    res.json({
      success: true,
      total,
      interest,
    });
  } catch (err) {
    res.status(500).json({ error: err });
  }
};

exports.delete_interest = async (req, res, next) => {
  const ability = defineAbilityFor(req.user);
  let id = req.params.id;
  try {
    let oninterest = await Interest.findById({ _id: id });
    ForbiddenError.from(ability).throwUnlessCan("delete", oninterest);
    await Interest.findByIdAndDelete(id);
    res.json({
      success: true,
    });
  } catch (err) {
    res.status(500).json({ error: err });
  }
};

exports.admin_change_email = async (req, res, next) => {
  const ability = defineAbilityFor(req.user);
  let _id = req.params.id;
  let email = req.body.email.toLowerCase();
  try {
    let user = User.findById({ _id });
    let findEmail = User.findOne({ email: email });
    let [foundUser, foundEmail] = await Promise.all([user, findEmail]);
    if (foundEmail) {
      return res.status(403).json({
        success: false,
        message: "Email is already in use",
      });
    }
    ForbiddenError.from(ability).throwUnlessCan("update", foundUser);
    foundUser.email = email;
    await foundUser.save();
    res.status(200).json({
      success: true,
      message: "Email has been change",
    });
  } catch (err) {
    res.json(500).status({
      error: err,
      success: false,
      message: "Server Error",
    });
  }
};

exports.admin_get_user_revenue_history = async (req, res, next) => {
  let host = mongoose.Types.ObjectId(req.params.id);
  let perPage = 20; // số lượng sản phẩm xuất hiện trên 1 page
  let page = parseInt(req.query.page) || 1;
  try {
    let onUser = User.findById({ _id: host }).select("email");
    let onTotal = Revenue.countDocuments({ host: host });
    let onRevenue = Revenue.find({
      host: host,
    })
      .sort({ updatedAt: "desc" })
      .skip(perPage * page - perPage) // Trong page đầu tiên sẽ bỏ qua giá trị là 0
      .limit(perPage)
      .populate("interest", "amount")
      .populate("partner", "email")
      .exec();
    let [total, revenue, user] = await Promise.all([
      onTotal,
      onRevenue,
      onUser,
    ]);
    res.status(200).json({
      revenue,
      success: true,
      total,
      user,
    });
  } catch (err) {
    res.status(500).json({ error: err });
  }
};

exports.admin_get_chart = async (req, res, next) => {
  let rule = req.user.rule;
  let ability = defineAbilityFor(req.user);
  let startFormat = new Date();
  let endFormat = new Date();
  startFormat.setDate(startFormat.getDate() - 7); // set to 'now' minus 7 days.
  startFormat.setHours(0, 0, 0, 0); // set to midnight.
  try {
    if (rule == "admin") {
      let onDepositChart = Deposit.aggregate([
        {
          $match: {
            $and: [
              { updatedAt: { $gte: startFormat } },
              { updatedAt: { $lte: endFormat } },
              { status: "Completed" },
            ],
          },
        },
        {
          $group: {
            _id: { $dateToString: { format: "%Y-%m-%d", date: "$updatedAt" } },
            list: { $push: "$$ROOT" },
            total: { $sum: { $multiply: ["$amount"] } },
            count: { $sum: 1 },
          },
        },
      ]);
      let [getDepositChart] = await Promise.all([onDepositChart]);
      ForbiddenError.from(ability).throwUnlessCan("read", getDepositChart);

      res.status(200).json({
        success: true,
        data: getDepositChart,
      });
    } else {
      res.status(500).json({
        success: false,
        mesage: "You can't accept this action",
      });
    }
  } catch (err) {
    res.status(500).json({
      error: err,
    });
  }
};

exports.admin_update_user_private = async (req, res, next) => {
  let _id = req.body.id;
  const ability = defineAbilityFor(req.user);
  try {
    let user = await User.findById({ _id });
    ForbiddenError.from(ability).throwUnlessCan("update", user);
    if (user) {
      user.private = !user.private;
    }
    await user.save();

    res.status(200).json({
      success: true,
      user,
    });
  } catch (err) {
    res.status(404).json({
      error: err,
    });
  }
};

exports.getAllUserForSelect = async (req, res) => {
  const email = { $regex: new RegExp(`^${req.query.q.toLowerCase()}`) };
  try {
    // console.log(email)
    // const onUsers = User.find({email})
    //   .sort({ createdAt: 'desc' })
    //   .select('email');
    // const [users] = await Promise.all([onUsers]);
    // console.log(users)
    const users = await User.aggregate([
      { $match: { email: email } },
      {
        $group: {
          _id: "$_id",
          email: { $first: "$email" },
          username: { $first: "$username" },
          phonenumber: { $first: "$phonenumber" },
        },
      },
      { $limit: 20 },
    ]);
    res.json({
      success: true,
      users,
    });
  } catch (err) {
    res.json({
      success: false,
      message: "something wrong",
    });
  }
};

exports.getProducts = async (req, res) => {
  // const ability = defineAbilityFor(req.user);
  const { perPage } = req.body || 20;
  const page = parseInt(req.body.page) || 1;
  try {
    const onTotal = Product.countDocuments();
    const onProducts = Product.find()
      .sort({ createdAt: "desc" })
      .skip(perPage * page - perPage) // Trong page đầu tiên sẽ bỏ qua giá trị là 0
      .limit(perPage);
    const [total, products] = await Promise.all([onTotal, onProducts]);
    // ForbiddenError.from(ability).throwUnlessCan("read", products);
    res.status(200).json({
      message: "this is all products",
      total,
      products,
      success: true,
    });
  } catch (err) {
    console.log(err);
    res.status(500).json({
      message: "Cant get products",
      error: err,
      success: false,
    });
  }
};

exports.getDashboardStats = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  try {
    // Get total posts
    const totalPosts = Post.countDocuments();

    // Get total users
    const totalUsers = User.countDocuments();

    // Get total pages
    const totalPages = Page.countDocuments();

    // Get pending posts (posts that are not active - waiting for approval)
    const pendingPosts = Post.countDocuments({ isActive: false });

    // Get active users (users who logged in within last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const activeUsers = User.countDocuments({
      lastLoginAt: { $gte: thirtyDaysAgo }
    });

    // Get total views (if you have a views field in posts)
    const totalViewsAgg = Post.aggregate([
      { $group: { _id: null, totalViews: { $sum: "$views" } } }
    ]);

    // Get analytics data
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const lastWeek = new Date(today);
    lastWeek.setDate(lastWeek.getDate() - 7);

    // Get analytics data
    const todayAnalytics = DailyAnalytics.findOne({ date: today });
    const yesterdayAnalytics = DailyAnalytics.findOne({ date: yesterday });
    const totalPageViews = PageView.countDocuments();
    const homeViewsToday = PageView.countDocuments({
      timestamp: { $gte: today },
      page: 'home'
    });
    const postViewsToday = PageView.countDocuments({
      timestamp: { $gte: today },
      page: 'post'
    });
    const weeklyViews = PageView.countDocuments({
      timestamp: { $gte: lastWeek }
    });

    const [
      totalPostsCount,
      totalUsersCount,
      totalPagesCount,
      pendingPostsCount,
      activeUsersCount,
      viewsResult,
      todayAnalyticsData,
      yesterdayAnalyticsData,
      totalPageViewsCount,
      homeViewsTodayCount,
      postViewsTodayCount,
      weeklyViewsCount
    ] = await Promise.all([
      totalPosts,
      totalUsers,
      totalPages,
      pendingPosts,
      activeUsers,
      totalViewsAgg,
      todayAnalytics,
      yesterdayAnalytics,
      totalPageViews,
      homeViewsToday,
      postViewsToday,
      weeklyViews
    ]);

    ForbiddenError.from(ability).throwUnlessCan("read", "dashboard");

    // Calculate growth percentages
    const homeViewsGrowth = yesterdayAnalyticsData && yesterdayAnalyticsData.homeViews > 0
      ? ((homeViewsTodayCount - yesterdayAnalyticsData.homeViews) / yesterdayAnalyticsData.homeViews * 100)
      : 0;

    const postViewsGrowth = yesterdayAnalyticsData && yesterdayAnalyticsData.postViews > 0
      ? ((postViewsTodayCount - yesterdayAnalyticsData.postViews) / yesterdayAnalyticsData.postViews * 100)
      : 0;

    const stats = {
      totalPosts: totalPostsCount,
      totalUsers: totalUsersCount,
      totalPages: totalPagesCount,
      totalViews: viewsResult.length > 0 ? viewsResult[0].totalViews : 0,
      pendingPosts: pendingPostsCount,
      activeUsers: activeUsersCount,
      // Analytics data
      analytics: {
        totalPageViews: totalPageViewsCount,
        homeViewsToday: homeViewsTodayCount,
        postViewsToday: postViewsTodayCount,
        totalViewsToday: todayAnalyticsData?.totalViews || 0,
        weeklyViews: weeklyViewsCount,
        uniqueVisitorsToday: todayAnalyticsData?.uniqueVisitors || 0,
        homeViewsGrowth: Math.round(homeViewsGrowth * 100) / 100,
        postViewsGrowth: Math.round(postViewsGrowth * 100) / 100
      }
    };

    // Disable caching for dashboard stats to ensure fresh data
    res.set({
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    });

    res.status(200).json({
      success: true,
      stats
    });
  } catch (err) {
    console.log(err);
    res.status(500).json({
      message: "Cannot get dashboard statistics",
      error: err,
      success: false,
    });
  }
};

exports.getRecentActivities = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  try {
    ForbiddenError.from(ability).throwUnlessCan("read", "dashboard");

    const limit = parseInt(req.query.limit) || 10;

    // Get recent posts (created and updated)
    const recentPosts = Post.find()
      .populate('user', 'username email')
      .sort({ updatedAt: -1 })
      .limit(limit)
      .select('title slug createdAt updatedAt user isActive');

    // Get recent users (registered)
    const recentUsers = User.find()
      .sort({ createdAt: -1 })
      .limit(limit)
      .select('username email createdAt rule');

    // Get recent pages (created and updated)
    const recentPages = Page.find()
      .populate('user', 'username email')
      .sort({ updatedAt: -1 })
      .limit(limit)
      .select('title slug createdAt updatedAt user');

    const [posts, users, pages] = await Promise.all([
      recentPosts,
      recentUsers,
      recentPages
    ]);

    // Combine and format activities
    const activities = [];

    // Add post activities
    posts.forEach(post => {
      const isNew = new Date(post.createdAt).getTime() === new Date(post.updatedAt).getTime();
      let action = '';
      let type = '';

      if (isNew) {
        action = post.isActive ? 'Bài viết mới được tạo' : 'Bài viết mới (chờ kích hoạt)';
        type = post.isActive ? 'post_created' : 'post_pending';
      } else {
        action = post.isActive ? 'Bài viết được cập nhật' : 'Bài viết được cập nhật (chờ kích hoạt)';
        type = post.isActive ? 'post_updated' : 'post_pending_updated';
      }

      activities.push({
        id: post._id,
        type: type,
        action: action,
        title: post.title,
        slug: post.slug,
        user: post.user ? post.user.username : 'Unknown',
        userId: post.user ? post.user._id : null,
        time: post.updatedAt,
        isActive: post.isActive
      });
    });

    // Add user activities
    users.forEach(user => {
      activities.push({
        id: user._id,
        type: 'user_registered',
        action: 'Thành viên mới đăng ký',
        title: user.username,
        user: user.username,
        userId: user._id,
        time: user.createdAt,
        role: user.rule
      });
    });

    // Add page activities
    pages.forEach(page => {
      const isNew = new Date(page.createdAt).getTime() === new Date(page.updatedAt).getTime();
      activities.push({
        id: page._id,
        type: isNew ? 'page_created' : 'page_updated',
        action: isNew ? 'Trang mới được tạo' : 'Trang được cập nhật',
        title: page.title,
        slug: page.slug,
        user: page.user ? page.user.username : 'Unknown',
        userId: page.user ? page.user._id : null,
        time: page.updatedAt
      });
    });

    // Sort by time (most recent first) and limit
    activities.sort((a, b) => new Date(b.time) - new Date(a.time));
    const recentActivities = activities.slice(0, limit);

    // Disable caching for recent activities to ensure fresh data
    res.set({
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    });

    res.status(200).json({
      success: true,
      activities: recentActivities
    });
  } catch (err) {
    console.log(err);
    res.status(500).json({
      message: "Cannot get recent activities",
      error: err,
      success: false,
    });
  }
};

exports.getPendingPosts = async (req, res) => {
  const ability = defineAbilityFor(req.user);
  try {
    ForbiddenError.from(ability).throwUnlessCan("read", "dashboard");

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Get pending posts (isActive: false)
    const pendingPosts = Post.find({ isActive: false })
      .populate('user', 'username email')
      .populate('categories', 'name slug')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .select('title slug short createdAt updatedAt user categories isActive isFeature views');

    // Get total count for pagination
    const totalCount = Post.countDocuments({ isActive: false });

    const [posts, total] = await Promise.all([pendingPosts, totalCount]);

    res.status(200).json({
      success: true,
      posts,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(total / limit),
        totalPosts: total,
        hasNextPage: page < Math.ceil(total / limit),
        hasPrevPage: page > 1
      }
    });
  } catch (err) {
    console.log(err);
    res.status(500).json({
      message: "Cannot get pending posts",
      error: err,
      success: false,
    });
  }
};
exports.searchProducts = async (req, res) => {
  const title = req.body.title;
  try {
    console.log(title);
    const products = await Product.aggregate([
      { $match: { title: { $regex: title, $options: "i" } } }, // Use $regex for partial matching, "i" makes it case-insensitive
      { $limit: 20 },
    ]);
    console.log(products);
    res.json({
      success: true,
      products,
    });
  } catch (err) {
    res.status(500).json({
      message: "Please check correctness and try again",
      error: err,
      success: false,
    });
  }
};



exports.adminChangeInfo = async (req, res, next) => {
  const ability = defineAbilityFor(req.user);
  const email = req.body.email.toLowerCase();
  const { username, phonenumber, gender, bio, idname, rank, private, _id, rule, categories } =
    req.body;
  try {
    const onUser = User.findById({ _id }).select(
      "username email avatar bio phonenumber gender idname store rule"
    );
    const onEmail = User.findOne({ email });
    const onPhone = User.findOne({ phonenumber });
    const [user, inEmail, inPhone] = await Promise.all([
      onUser,
      onEmail,
      onPhone,
    ]);
    if (inEmail && inEmail.email !== user.email) {
      res.status(501).json({
        success: false,
        message: "Email này đã được dùng cho tài khoản khác",
      });
      return;
    } else if (inPhone && inPhone.phonenumber !== user.phonenumber) {
      res.status(502).json({
        success: false,
        message: "Số điện thoại này đã được dùng cho tài khoản khác",
      });
      return;
    } else {
      ForbiddenError.from(ability).throwUnlessCan("update", user);
      if (user) {
        user.email = email;
        user.username = username;
        user.idname = idname;
        user.phonenumber = phonenumber;
        user.bio = bio;
        user.gender = gender;
        user.rank = rank;
        user.private = private;
        user.rule = rule
      }
      user.categories = [];
      if (categories) {
        // Handle array format
        if (Array.isArray(categories)) {
          for (const u of categories) {
            const catId = {};
            catId._id = u;
            user.categories.push(catId);
          }
        } 
        // Handle object with numeric keys (converted array)
        else if (typeof categories === "object") {
          const keys = Object.keys(categories);
          const isArrayLike = keys.every(key => /^\d+$/.test(key));
          if (isArrayLike) {
            // Convert object back to array format
            for (const key of keys) {
              const catId = {};
              catId._id = categories[key];
              user.categories.push(catId);
            }
          } else {
            // Single object format
            const catId = {};
            catId._id = categories._id || categories;
            user.categories.push(catId);
          }
        } 
        // Handle single string format
        else {
          user.categories.push({
            _id: categories,
          });
        }
      }
      await user.save();
      res.status(200).json({
        success: true,
        message: "Success",
        user
      });
    }
  } catch (err) {
    res.json(500).status({
      error: err,
      success: false,
      message: "Server Error",
    });
  }
};

exports.getUserLog = async (req, res, next) => {

  const ability = defineAbilityFor(req.user);
  const _id = req.params.id;

  try {
    const user = await User.findById(_id).select('username email')
   
    if (!user) {
      return res.status(404).json({ status: false, message: "User not found" });
    }

    ForbiddenError.from(ability).throwUnlessCan("read", user);

    // Fetch latest 10 user logs
    const userLogs = await UserLog.find({ user: user._id })
      .sort({ loginTime: -1 }) // Sort by newest first
      .limit(10) // Limit to last 10 logs
      .lean(); // Convert Mongoose objects to plain JS
    res.status(200).json({ status: true, userLogs, success: true, user });
  } catch (err) {
    console.error("Error fetching user logs:", err);
    res.status(500).json({ status: false, message: "Internal Server Error", error: err.message });
  }
};
