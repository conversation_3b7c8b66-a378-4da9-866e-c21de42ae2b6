"use client";

import { useState, useEffect } from "react";
import { Card } from "@/components/ui/Card";
import { toast } from "react-toastify";
import {
  Home,
  Plus,
  Edit3,
  Trash2,
  Users,
  Save,
  X,
  RefreshCw,
  Move
} from "react-feather";
import departmentApiRequest, { Department } from "@/apiRequests/department";
import PositionManagement from "@/components/PositionManagement";
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';

const ItemType = 'DEPARTMENT';

interface DraggableDepartmentProps {
  department: Department;
  index: number;
  editingId: string | null;
  editDepartmentName: string;
  setEditingId: (id: string | null) => void;
  setEditDepartmentName: (name: string) => void;
  handleUpdateDepartment: (id: string) => void;
  handleDeleteDepartment: (id: string) => void;
  sessionToken: string;
  moveDepartment: (dragIndex: number, hoverIndex: number) => void;
}

function DraggableDepartment({
  department,
  index,
  editingId,
  editDepartmentName,
  setEditingId,
  setEditDepartmentName,
  handleUpdateDepartment,
  handleDeleteDepartment,
  sessionToken,
  moveDepartment
}: DraggableDepartmentProps) {
  const [{ isDragging }, drag] = useDrag({
    type: ItemType,
    item: { index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const [, drop] = useDrop({
    accept: ItemType,
    hover: (draggedItem: { index: number }) => {
      if (draggedItem.index !== index) {
        moveDepartment(draggedItem.index, index);
        draggedItem.index = index;
      }
    },
  });

  return (
    <div
      ref={(node) => drag(drop(node))}
      className={`border-b border-gray-200 last:border-b-0 ${isDragging ? 'opacity-50' : ''}`}
      style={{ cursor: 'move' }}
    >
      <div className="p-6 flex items-center justify-between hover:bg-gray-50">
        <div className="flex items-center space-x-4">
          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
            <Move size={20} className="text-blue-600" />
          </div>
          <div>
            {editingId === department._id ? (
              <input
                type="text"
                value={editDepartmentName}
                onChange={(e) => setEditDepartmentName(e.target.value)}
                className="text-lg font-semibold text-gray-900 bg-white border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
                onKeyPress={(e) => e.key === 'Enter' && handleUpdateDepartment(department._id)}
                autoFocus
              />
            ) : (
              <>
                <h3 className="text-lg font-semibold text-gray-900">{department.name}</h3>
                <p className="text-sm text-gray-500">Level: {department.level || 5}</p>
              </>
            )}
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {editingId === department._id ? (
            <>
              <button
                onClick={() => handleUpdateDepartment(department._id)}
                className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors"
              >
                <Save size={16} />
              </button>
              <button
                onClick={() => setEditingId(null)}
                className="p-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors"
              >
                <X size={16} />
              </button>
            </>
          ) : (
            <>
              <button
                onClick={() => {
                  setEditingId(department._id);
                  setEditDepartmentName(department.name);
                }}
                className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
              >
                <Edit3 size={16} />
              </button>
              <button
                onClick={() => handleDeleteDepartment(department._id)}
                className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
              >
                <Trash2 size={16} />
              </button>
            </>
          )}
        </div>
      </div>

      {/* Position Management Component */}
      <PositionManagement
        departmentId={department._id}
        departmentName={department.name}
        sessionToken={sessionToken}
      />
    </div>
  );
}

export default function DepartmentsManagement() {
  const [sessionToken, setSessionToken] = useState<string>("");
  const [departments, setDepartments] = useState<Department[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [newDepartmentName, setNewDepartmentName] = useState("");
  const [editDepartmentName, setEditDepartmentName] = useState("");
  const [isReordering, setIsReordering] = useState(false);

  // Get session token on client side only
  useEffect(() => {
    const token = localStorage.getItem("sessionToken") || "";
    setSessionToken(token);
  }, []);

  // Move department function for drag and drop
  const moveDepartment = (dragIndex: number, hoverIndex: number) => {
    const draggedDepartment = departments[dragIndex];
    const newDepartments = [...departments];
    newDepartments.splice(dragIndex, 1);
    newDepartments.splice(hoverIndex, 0, draggedDepartment);
    setDepartments(newDepartments);
  };

  // Save department order
  const saveDepartmentOrder = async () => {
    if (!sessionToken) return;

    try {
      setIsReordering(true);

      // Create department order array with updated levels
      const departmentOrder = departments.map((dept, index) => ({
        _id: dept._id,
        level: index + 1 // Level starts from 1
      }));

      const result = await departmentApiRequest.reorderDepartments(sessionToken, departmentOrder);

      if (result.payload.success) {
        toast.success("Cập nhật thứ tự phòng ban thành công");
        // Update local state with new levels
        const updatedDepartments = departments.map((dept, index) => ({
          ...dept,
          level: index + 1
        }));
        setDepartments(updatedDepartments);
      } else {
        toast.error(result.payload.message || "Lỗi khi cập nhật thứ tự phòng ban");
        // Refresh departments to revert changes
        await fetchDepartments();
      }
    } catch (error) {
      console.error("Error saving department order:", error);
      toast.error("Lỗi khi cập nhật thứ tự phòng ban");
      // Refresh departments to revert changes
      await fetchDepartments();
    } finally {
      setIsReordering(false);
    }
  };

  // Start editing a department
  const startEdit = (department: Department) => {
    setEditingId(department._id);
    setEditDepartmentName(department.name);
  };

  // Cancel editing
  const cancelEdit = () => {
    setEditingId(null);
    setEditDepartmentName("");
  };

  // Fetch departments
  const fetchDepartments = async () => {
    if (!sessionToken) {
      console.log("fetchDepartments: No session token available");
      setLoading(false);
      return;
    }

    try {
      console.log("fetchDepartments: Starting fetch with token");
      setLoading(true);
      const result = await departmentApiRequest.getDepartments(sessionToken);
      console.log("fetchDepartments: API result:", result);
      
      if (result.payload.success) {
        console.log("fetchDepartments: Success, setting departments:", result.payload.data);
        setDepartments(result.payload.data || []);
        toast.success("Đã tải danh sách phòng ban thành công");
      } else {
        console.error("API Error:", result.payload.message);
        toast.error(result.payload.message || "Lỗi khi tải danh sách phòng ban");
      }
    } catch (error: any) {
      console.error("Error fetching departments:", error);
      if (error?.status === 401) {
        toast.error("Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.");
      } else if (error?.status === 408) {
        toast.error("Timeout: Không thể tải danh sách phòng ban. Vui lòng thử lại.");
      } else if (error?.message?.includes('timeout') || error?.message?.includes('Timeout')) {
        toast.error("Timeout: Không thể tải danh sách phòng ban. Vui lòng thử lại.");
      } else {
        toast.error("Lỗi khi tải danh sách phòng ban");
      }
    } finally {
      setLoading(false);
    }
  };

  // Create department
  const handleCreateDepartment = async () => {
    if (!sessionToken || !newDepartmentName.trim()) return;

    try {
      console.log("Creating department:", newDepartmentName.trim());
      const result = await departmentApiRequest.createDepartment(sessionToken, newDepartmentName.trim());
      console.log("Create department result:", result);
      
      if (result.payload.success) {
        toast.success(result.payload.message || "Tạo phòng ban thành công");
        setNewDepartmentName("");
        setShowAddForm(false);
        
        // Refresh the departments list
        console.log("Refreshing departments list after creation");
        await fetchDepartments();
      } else {
        toast.error(result.payload.message || "Lỗi khi tạo phòng ban");
      }
    } catch (error: any) {
      console.error("Error creating department:", error);
      toast.error("Lỗi khi tạo phòng ban");
    }
  };

  // Update department
  const handleUpdateDepartment = async (id: string) => {
    if (!sessionToken || !editDepartmentName.trim()) return;

    try {
      console.log("Updating department:", id, editDepartmentName.trim());
      const result = await departmentApiRequest.updateDepartment(sessionToken, id, editDepartmentName.trim());
      console.log("Update department result:", result);
      
      if (result.payload.success) {
        toast.success(result.payload.message || "Cập nhật phòng ban thành công");
        setEditingId(null);
        setEditDepartmentName("");
        
        // Refresh the departments list
        console.log("Refreshing departments list after update");
        await fetchDepartments();
      } else {
        toast.error(result.payload.message || "Lỗi khi cập nhật phòng ban");
      }
    } catch (error: any) {
      console.error("Error updating department:", error);
      toast.error("Lỗi khi cập nhật phòng ban");
    }
  };

  // Delete department
  const handleDeleteDepartment = async (id: string, name: string) => {
    if (!sessionToken) return;

    if (!confirm(`Bạn có chắc chắn muốn xóa phòng ban "${name}"?`)) return;

    try {
      console.log("Deleting department:", id, name);
      const result = await departmentApiRequest.deleteDepartment(sessionToken, id);
      console.log("Delete department result:", result);
      
      if (result.payload.success) {
        toast.success(result.payload.message || "Xóa phòng ban thành công");
        
        // Refresh the departments list
        console.log("Refreshing departments list after deletion");
        await fetchDepartments();
      } else {
        toast.error(result.payload.message || "Lỗi khi xóa phòng ban");
      }
    } catch (error: any) {
      console.error("Error deleting department:", error);
      toast.error("Lỗi khi xóa phòng ban");
    }
  };



  useEffect(() => {
    console.log("Department page - sessionToken:", sessionToken ? "exists" : "missing");
    
    if (!sessionToken) {
      setLoading(false);
      return;
    }

    // Set timeout to prevent infinite loading
    const timeout = setTimeout(() => {
      if (loading) {
        setLoading(false);
        toast.error("Timeout: Không thể tải danh sách phòng ban");
      }
    }, 10000); // 10 seconds timeout

    fetchDepartments();

    return () => clearTimeout(timeout);
  }, [sessionToken]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Đang tải danh sách phòng ban...</p>
          <p className="mt-2 text-sm text-gray-500">
            Token: {sessionToken ? "✓" : "✗"}
          </p>
        </div>
      </div>
    );
  }

  if (!sessionToken) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-red-600">Không có quyền truy cập. Vui lòng đăng nhập lại.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <Home className="h-8 w-8 text-blue-600" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Quản lý Phòng ban</h1>
            <p className="text-gray-600">Quản lý danh sách các phòng ban trong tổ chức</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={fetchDepartments}
            className="flex items-center space-x-2 bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
            disabled={loading}
          >
            <RefreshCw size={20} className={loading ? "animate-spin" : ""} />
            <span>Làm mới</span>
          </button>
          <button
            onClick={() => setShowAddForm(true)}
            className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus size={20} />
            <span>Thêm phòng ban</span>
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Tổng phòng ban</p>
              <p className="text-2xl font-bold text-gray-900">{departments.length}</p>
            </div>
            <Home className="h-8 w-8 text-blue-600" />
          </div>
        </Card>
      </div>

      {/* Add Form */}
      {showAddForm && (
        <Card className="p-6 mb-6">
          <h3 className="text-lg font-semibold mb-4">Thêm phòng ban mới</h3>
          <div className="flex items-center space-x-4">
            <input
              type="text"
              value={newDepartmentName}
              onChange={(e) => setNewDepartmentName(e.target.value)}
              placeholder="Tên phòng ban"
              className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              onKeyPress={(e) => e.key === 'Enter' && handleCreateDepartment()}
            />
            <button
              onClick={handleCreateDepartment}
              disabled={!newDepartmentName.trim()}
              className="flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <Save size={16} />
              <span>Lưu</span>
            </button>
            <button
              onClick={() => {
                setShowAddForm(false);
                setNewDepartmentName("");
              }}
              className="flex items-center space-x-2 bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors"
            >
              <X size={16} />
              <span>Hủy</span>
            </button>
          </div>
        </Card>
      )}

      {/* Departments List */}
      <Card className="overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-semibold text-gray-900">Danh sách phòng ban</h2>
              <p className="text-sm text-gray-600 mt-1">Kéo thả để sắp xếp thứ tự phòng ban</p>
            </div>
            <button
              onClick={saveDepartmentOrder}
              disabled={isReordering}
              className="flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isReordering ? (
                <RefreshCw size={16} className="animate-spin" />
              ) : (
                <Save size={16} />
              )}
              <span>{isReordering ? "Đang lưu..." : "Lưu thứ tự"}</span>
            </button>
          </div>
        </div>

        {departments.length === 0 ? (
          <div className="p-12 text-center">
            <Home className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Chưa có phòng ban nào</h3>
            <p className="text-gray-600 mb-4">Bắt đầu bằng cách thêm phòng ban đầu tiên</p>
            <button
              onClick={() => setShowAddForm(true)}
              className="inline-flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Plus size={20} />
              <span>Thêm phòng ban</span>
            </button>
          </div>
        ) : (
          <DndProvider backend={HTML5Backend}>
            <div className="divide-y divide-gray-200">
              {departments.map((department, index) => (
                <DraggableDepartment
                  key={department._id}
                  department={department}
                  index={index}
                  editingId={editingId}
                  editDepartmentName={editDepartmentName}
                  setEditingId={setEditingId}
                  setEditDepartmentName={setEditDepartmentName}
                  handleUpdateDepartment={handleUpdateDepartment}
                  handleDeleteDepartment={handleDeleteDepartment}
                  sessionToken={sessionToken}
                  moveDepartment={moveDepartment}
                />
              ))}
            </div>
          </DndProvider>
        )}
      </Card>
    </div>
  );
}
