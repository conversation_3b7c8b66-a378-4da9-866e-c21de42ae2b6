"use client";
import { useState, useEffect } from "react";
import { Card } from "@/components/ui/Card";
import { toast } from "react-toastify";
import {
  Plus,
  Edit3,
  Trash2,
  Shield,
  Award,
  Settings,
  Save,
  X,
  Users,
  ChevronUp,
  ChevronDown
} from "react-feather";

interface Position {
  _id: string;
  name: string;
  description: string;
  level: number;
  permissions: string[];
  isActive: boolean;
  isDefault: boolean;
  order: number;
  createdBy?: {
    _id: string;
    username: string;
  };
  createdAt: string;
  updatedAt: string;
}

interface Permission {
  key: string;
  name: string;
  description: string;
}

const PositionsManagement = () => {
  const [positions, setPositions] = useState<Position[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(true);
  const [sessionToken, setSessionToken] = useState<string>("");
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingPosition, setEditingPosition] = useState<Position | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    level: 1,
    permissions: [] as string[],
    order: 0
  });

  useEffect(() => {
    const token = localStorage.getItem("sessionToken");
    if (token) {
      setSessionToken(token);
      fetchPositions(token);
      fetchPermissions(token);
    } else {
      setLoading(false);
      toast.error("Vui lòng đăng nhập");
    }
  }, []);

  const fetchPositions = async (token: string) => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_ENDPOINT}/positions`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setPositions(data.positions || []);
      } else {
        throw new Error("Không thể tải danh sách chức vụ");
      }
    } catch (error) {
      console.error("Error fetching positions:", error);
      toast.error("Không thể tải danh sách chức vụ");
    } finally {
      setLoading(false);
    }
  };

  const fetchPermissions = async (token: string) => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_ENDPOINT}/positions/permissions`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setPermissions(data.permissions || []);
      }
    } catch (error) {
      console.error("Error fetching permissions:", error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast.error("Tên chức vụ không được để trống");
      return;
    }

    try {
      const url = editingPosition 
        ? `${process.env.NEXT_PUBLIC_API_ENDPOINT}/positions/${editingPosition._id}`
        : `${process.env.NEXT_PUBLIC_API_ENDPOINT}/positions`;
      
      const method = editingPosition ? "PUT" : "POST";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${sessionToken}`,
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        toast.success(editingPosition ? "Cập nhật chức vụ thành công" : "Tạo chức vụ thành công");
        setShowAddModal(false);
        setEditingPosition(null);
        resetForm();
        fetchPositions(sessionToken);
      } else {
        const data = await response.json();
        throw new Error(data.message || "Có lỗi xảy ra");
      }
    } catch (error: any) {
      console.error("Error saving position:", error);
      toast.error(error.message || "Có lỗi xảy ra");
    }
  };

  const handleDelete = async (positionId: string) => {
    if (!confirm("Bạn có chắc chắn muốn xóa chức vụ này?")) return;

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_ENDPOINT}/positions/${positionId}`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${sessionToken}`,
        },
      });

      if (response.ok) {
        toast.success("Xóa chức vụ thành công");
        fetchPositions(sessionToken);
      } else {
        const data = await response.json();
        throw new Error(data.message || "Không thể xóa chức vụ");
      }
    } catch (error: any) {
      console.error("Error deleting position:", error);
      toast.error(error.message || "Không thể xóa chức vụ");
    }
  };

  const handleEdit = (position: Position) => {
    setEditingPosition(position);
    setFormData({
      name: position.name,
      description: position.description,
      level: position.level,
      permissions: position.permissions,
      order: position.order
    });
    setShowAddModal(true);
  };

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      level: 1,
      permissions: [],
      order: 0
    });
  };

  const handlePermissionChange = (permissionKey: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      permissions: checked 
        ? [...prev.permissions, permissionKey]
        : prev.permissions.filter(p => p !== permissionKey)
    }));
  };

  const getLevelColor = (level: number) => {
    if (level >= 9) return "bg-red-100 text-red-800";
    if (level >= 7) return "bg-orange-100 text-orange-800";
    if (level >= 5) return "bg-yellow-100 text-yellow-800";
    if (level >= 3) return "bg-blue-100 text-blue-800";
    return "bg-gray-100 text-gray-800";
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-2">
            <Shield className="text-blue-600" />
            Quản lý Chức vụ
          </h1>
          <p className="text-gray-600 mt-1">Quản lý các chức vụ và quyền hạn trong hệ thống</p>
        </div>
        <button
          onClick={() => {
            setEditingPosition(null);
            resetForm();
            setShowAddModal(true);
          }}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
        >
          <Plus className="w-4 h-4" />
          Thêm chức vụ
        </button>
      </div>

      {/* Positions Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {positions.map((position) => (
          <Card key={position._id} className="p-6 hover:shadow-lg transition-shadow">
            <div className="flex justify-between items-start mb-4">
              <div className="flex items-center gap-3">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center ${getLevelColor(position.level)}`}>
                  <Award className="w-5 h-5" />
                </div>
                <div>
                  <h3 className="font-semibold text-lg text-gray-900">{position.name}</h3>
                  <span className={`text-xs px-2 py-1 rounded-full ${getLevelColor(position.level)}`}>
                    Cấp {position.level}
                  </span>
                </div>
              </div>
              {!position.isDefault && (
                <div className="flex gap-1">
                  <button
                    onClick={() => handleEdit(position)}
                    className="p-1 text-blue-600 hover:bg-blue-50 rounded"
                    title="Chỉnh sửa"
                  >
                    <Edit3 className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleDelete(position._id)}
                    className="p-1 text-red-600 hover:bg-red-50 rounded"
                    title="Xóa"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              )}
            </div>

            <p className="text-gray-600 text-sm mb-4">{position.description}</p>

            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Users className="w-4 h-4 text-gray-400" />
                <span className="text-sm text-gray-600">
                  {position.permissions.length} quyền
                </span>
              </div>

              <div className="flex flex-wrap gap-1">
                {position.permissions.slice(0, 3).map((perm) => {
                  const permission = permissions.find(p => p.key === perm);
                  return (
                    <span
                      key={perm}
                      className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded"
                    >
                      {permission?.name || perm}
                    </span>
                  );
                })}
                {position.permissions.length > 3 && (
                  <span className="text-xs text-gray-500">
                    +{position.permissions.length - 3} quyền khác
                  </span>
                )}
              </div>

              {position.isDefault && (
                <div className="flex items-center gap-2 text-xs text-amber-600">
                  <Shield className="w-3 h-3" />
                  Chức vụ mặc định
                </div>
              )}
            </div>

            <div className="mt-4 pt-4 border-t border-gray-100">
              <div className="text-xs text-gray-500">
                Tạo: {new Date(position.createdAt).toLocaleDateString("vi-VN")}
                {position.createdBy && (
                  <span> bởi {position.createdBy.username}</span>
                )}
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Add/Edit Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold">
                  {editingPosition ? "Chỉnh sửa chức vụ" : "Thêm chức vụ mới"}
                </h2>
                <button
                  onClick={() => {
                    setShowAddModal(false);
                    setEditingPosition(null);
                    resetForm();
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>

              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tên chức vụ *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Nhập tên chức vụ"
                    required
                    disabled={editingPosition?.isDefault}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Mô tả
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Mô tả về chức vụ"
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Cấp độ (1-10)
                    </label>
                    <input
                      type="number"
                      min="1"
                      max="10"
                      value={formData.level}
                      onChange={(e) => setFormData({ ...formData, level: parseInt(e.target.value) })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      disabled={editingPosition?.isDefault}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Thứ tự hiển thị
                    </label>
                    <input
                      type="number"
                      min="0"
                      value={formData.order}
                      onChange={(e) => setFormData({ ...formData, order: parseInt(e.target.value) })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Quyền hạn
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {permissions.map((permission) => (
                      <label key={permission.key} className="flex items-start gap-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                        <input
                          type="checkbox"
                          checked={formData.permissions.includes(permission.key)}
                          onChange={(e) => handlePermissionChange(permission.key, e.target.checked)}
                          className="mt-1"
                        />
                        <div className="flex-1">
                          <div className="font-medium text-sm">{permission.name}</div>
                          <div className="text-xs text-gray-500">{permission.description}</div>
                        </div>
                      </label>
                    ))}
                  </div>
                </div>

                <div className="flex justify-end gap-3 pt-6 border-t">
                  <button
                    type="button"
                    onClick={() => {
                      setShowAddModal(false);
                      setEditingPosition(null);
                      resetForm();
                    }}
                    className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                  >
                    Hủy
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md flex items-center gap-2 transition-colors"
                  >
                    <Save className="w-4 h-4" />
                    {editingPosition ? "Cập nhật" : "Tạo mới"}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PositionsManagement;
