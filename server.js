const http = require('http');
require('dotenv').config();
const { validateEnvironment, getSecureDefaults } = require('./api/middleware/environmentValidation');

// Validate environment before starting server
validateEnvironment();

// Get secure configuration defaults
const config = getSecureDefaults();

const port = config.PORT;
const app = require('./app');

// Import Position model để khởi tạo default positions
const Position = require('./api/models/position');

const server = http.createServer(app);

// Enhanced server startup with security logging
server.listen(port, async () => {
  console.log(`🚀 Server started on port ${port}`);
  console.log(`🌍 Environment: ${config.NODE_ENV}`);
  console.log(`🔒 Security features enabled`);
  
  // Khởi tạo default positions
  try {
    await Position.createDefaultPositions();
    console.log('✅ Default positions initialized');
  } catch (error) {
    console.error('❌ Error initializing default positions:', error.message);
  }
  
  if (config.NODE_ENV === 'production') {
    console.log('🛡️  Production security mode activated');
  }
});

// Graceful shutdown handling
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🛑 SIGINT received, shutting down gracefully');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});
