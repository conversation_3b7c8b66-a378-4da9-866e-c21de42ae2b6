import { NextRequest, NextResponse } from "next/server";

// PUT - Reorder departments by updating their level values (admin only)
export async function PUT(request: NextRequest) {
  try {
    const token = request.headers.get("authorization")?.replace("Bearer ", "");
    if (!token) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { departmentOrder } = body;

    if (!Array.isArray(departmentOrder)) {
      return NextResponse.json(
        { success: false, message: "departmentOrder phải là một mảng" },
        { status: 400 }
      );
    }

    // Validate that all items have required fields
    for (let i = 0; i < departmentOrder.length; i++) {
      const item = departmentOrder[i];
      if (!item._id || typeof item.level !== 'number') {
        return NextResponse.json(
          { success: false, message: `Phần tử thứ ${i + 1} thiếu _id hoặc level` },
          { status: 400 }
        );
      }
    }

    const backendUrl = process.env.BACKEND_URL || "http://localhost:8000";
    
    // Create an AbortController for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    let response: Response;
    try {
      response = await fetch(`${backendUrl}/api/departments/reorder/bulk`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`,
        },
        body: JSON.stringify({ departmentOrder }),
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
    } catch (error: any) {
      clearTimeout(timeoutId);
      if (error.name === 'AbortError') {
        console.error("Department reorder API request timeout");
        return NextResponse.json(
          { success: false, message: "Timeout: Không thể cập nhật thứ tự phòng ban" },
          { status: 408 }
        );
      }
      throw error;
    }

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        { 
          success: false,
          message: data.message || "Lỗi server khi cập nhật thứ tự phòng ban" 
        },
        { status: response.status }
      );
    }

    return NextResponse.json({
      success: true,
      data: data.data,
      message: data.message || "Cập nhật thứ tự phòng ban thành công"
    });

  } catch (error) {
    console.error("Error in department reorder API:", error);
    return NextResponse.json(
      { success: false, message: "Lỗi server" },
      { status: 500 }
    );
  }
}
