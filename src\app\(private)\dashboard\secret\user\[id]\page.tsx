"use client";
import { useEffect, useState, use } from "react";
import EditForm from "@/app/(private)/dashboard/secret/user/add/edit-form";
import { AdminEditResBodyType, PasswordResBodyType } from "@/schemaValidations/user.schema"; // Import the schema
import userApiRequest from "@/apiRequests/user";
import { toast } from "react-toastify";
import categoryApiRequest from "@/apiRequests/category";
import Link from "next/link";
import { getErrorMessage } from "@/utils/errorHandler";


export default function EditUser({ params }: { params: Promise<{ id: any }> }) {
  const [user, setUser] = useState<AdminEditResBodyType | null>(null);
  const resolvedParams = use(params);
  const userId = resolvedParams.id;
  const [categories, setCategories] = useState([]);

  useEffect(() => {
    const controller = new AbortController();
    const { signal } = controller;
  
    const fetchUser = async () => {
      try {
        const sessionToken = localStorage.getItem("sessionToken") || "";
        console.log("Fetching user with ID:", userId);
        console.log("Session token exists:", !!sessionToken);

        const result = await userApiRequest.fetchUserById(userId, sessionToken, signal);
        const resCategories = await categoryApiRequest.fetchAllCategories(signal);

        if (!signal.aborted) {
          console.log("User fetch result:", result);
          if (result.payload.success) {
            setUser(result.payload.user);
            setCategories(resCategories.payload.categories);
          } else {
            console.error("Error fetching user:", result.message);
            toast.error("Failed to fetch user data: " + result.message);
          }
        }
      } catch (error) {
        if (!signal.aborted) {
          console.error("Unexpected error:", error);
          toast.error("An error occurred while fetching user data");
        }
      }
    };
  
    if (userId) {
      fetchUser();
    }
  
    return () => {
      controller.abort(); // Cleanup function to cancel the request
    };
  }, [userId]);

  const handleUpdate = async (data: AdminEditResBodyType) => {
    try {
      console.log("=== Frontend Debug: Sending data ===");
      console.log("Full data:", JSON.stringify(data, null, 2));
      console.log("Categories field:", data.categories);
      console.log("Categories type:", typeof data.categories);
      console.log("Categories isArray:", Array.isArray(data.categories));
      console.log("==================================");
      
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const result = await userApiRequest.updateUser( data, sessionToken );
      
      if (result.payload.success) {
        setUser(result.payload.user);
        toast.success("Update successful!");
      } else {
        console.error("Error updating user:", result.message);
        toast.error(result.payload?.message || "Có lỗi xảy ra khi cập nhật thông tin.");
      }
    } catch (error: any) {
      console.error("Unexpected error:", error);
      toast.error(getErrorMessage(error));
    } finally {
      setLoading(false);
    }
  };

  const onSubmitPass = async (data: PasswordResBodyType) => {
    try {
      const sessionToken = localStorage.getItem("sessionToken") || "";
      const result = await userApiRequest.updatePassUser( data, sessionToken );
      if (result.payload.success) {
        toast.success("Update successful!");
      } else {
        console.error("Error updating password:", result.payload?.message);
        toast.error(getErrorMessage(result));
      }
    } catch (error: any) {
      console.error("Unexpected error:", error);
      toast.error(getErrorMessage(error));
    }
  };

  return (
    <>
      <h1 className="text-2xl mb-4">Chỉnh sửa tài khoản</h1>
      <Link className="text-primary mb-2 block" href={`/dashboard/secret/blog?user=${user?._id}`}>Xem bài viết của thành viên</Link>
      <Link className="text-primary mb-2 block" href={`/dashboard/secret/user/log/${user?._id}`}>Xem User log</Link>
      {user ? (
        <EditForm onSubmit={handleUpdate} onSubmitPass={onSubmitPass} user={user} oncategories={categories} />
      ) : (
        <p>Loading...</p>
      )}
    </>
  );
}
