const express = require("express");
const router = express.Router();
const mongoose = require("mongoose");
const positionController = require("../controllers/position");
const passport = require("passport");
const { body, param, validationResult } = require("express-validator");

// Middleware để xử lý validation errors
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: "<PERSON><PERSON> liệu không hợp lệ",
      errors: errors.array()
    });
  }
  next();
};

// Middleware kiểm tra quyền admin
const requireAdmin = (req, res, next) => {
  if (req.user.rule !== "admin") {
    return res.status(403).json({
      success: false,
      message: "Chỉ admin mới có quyền thực hiện thao tá<PERSON> n<PERSON>"
    });
  }
  next();
};

// Validation rules
const createPositionValidation = [
  body("name")
    .trim()
    .notEmpty()
    .withMessage("Tên chức vụ không được để trống")
    .isLength({ min: 2, max: 50 })
    .withMessage("Tên chức vụ phải từ 2-50 ký tự"),
  body("description")
    .optional()
    .isLength({ max: 200 })
    .withMessage("Mô tả không được quá 200 ký tự"),
  body("permissions")
    .optional()
    .isArray()
    .withMessage("Permissions phải là một mảng"),
  body("level")
    .optional()
    .isInt({ min: 1, max: 10 })
    .withMessage("Level phải là số từ 1-10"),
  body("order")
    .optional()
    .isInt({ min: 0 })
    .withMessage("Order phải là số không âm")
];

const updatePositionValidation = [
  param("id").isMongoId().withMessage("ID không hợp lệ"),
  body("name")
    .optional()
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage("Tên chức vụ phải từ 2-50 ký tự"),
  body("description")
    .optional()
    .isLength({ max: 200 })
    .withMessage("Mô tả không được quá 200 ký tự"),
  body("permissions")
    .optional()
    .isArray()
    .withMessage("Permissions phải là một mảng"),
  body("level")
    .optional()
    .isInt({ min: 1, max: 10 })
    .withMessage("Level phải là số từ 1-10"),
  body("order")
    .optional()
    .isInt({ min: 0 })
    .withMessage("Order phải là số không âm"),
  body("isActive")
    .optional()
    .isBoolean()
    .withMessage("isActive phải là boolean")
];

// Routes

// GET /api/positions - Lấy tất cả positions
router.get(
  "/",
  passport.authenticate("admin", { session: false }),
  requireAdmin,
  positionController.getAllPositions
);

// GET /api/positions/permissions - Lấy danh sách permissions
router.get(
  "/permissions",
  passport.authenticate("admin", { session: false }),
  requireAdmin,
  positionController.getAvailablePermissions
);

// GET /api/positions/:id - Lấy position theo ID
router.get(
  "/:id",
  passport.authenticate("admin", { session: false }),
  requireAdmin,
  param("id").isMongoId().withMessage("ID không hợp lệ"),
  handleValidationErrors,
  positionController.getPositionById
);

// POST /api/positions - Tạo position mới
router.post(
  "/",
  passport.authenticate("admin", { session: false }),
  requireAdmin,
  ...createPositionValidation,
  handleValidationErrors,
  positionController.createPosition
);

// PUT /api/positions/:id - Cập nhật position
router.put(
  "/:id",
  passport.authenticate("admin", { session: false }),
  requireAdmin,
  ...updatePositionValidation,
  handleValidationErrors,
  positionController.updatePosition
);

// DELETE /api/positions/:id - Xóa position
router.delete(
  "/:id",
  passport.authenticate("admin", { session: false }),
  requireAdmin,
  param("id").isMongoId().withMessage("ID không hợp lệ"),
  handleValidationErrors,
  positionController.deletePosition
);

// PUT /api/positions/order - Cập nhật thứ tự positions
router.put(
  "/order",
  passport.authenticate("admin", { session: false }),
  requireAdmin,
  body("positions")
    .isArray()
    .withMessage("positions phải là một mảng")
    .custom((positions) => {
      for (const item of positions) {
        if (!item.id || !mongoose.Types.ObjectId.isValid(item.id)) {
          throw new Error("ID không hợp lệ");
        }
        if (typeof item.order !== 'number' || item.order < 0) {
          throw new Error("Order phải là số không âm");
        }
      }
      return true;
    }),
  handleValidationErrors,
  positionController.updatePositionsOrder
);

module.exports = router;
