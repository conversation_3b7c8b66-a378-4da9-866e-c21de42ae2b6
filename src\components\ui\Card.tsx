import React from 'react';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  padding?: 'none' | 'sm' | 'md' | 'lg';
  shadow?: 'none' | 'sm' | 'md' | 'lg';
  hover?: boolean;
  onClick?: () => void;
}

export const Card: React.FC<CardProps> = ({
  children,
  className = '',
  padding = 'md',
  shadow = 'sm',
  hover = false,
  onClick,
}) => {
  const paddingClasses = {
    none: '',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
  };

  const shadowClasses = {
    none: '',
    sm: 'shadow-sm',
    md: 'shadow-md',
    lg: 'shadow-lg',
  };

  const hoverClass = hover ? 'hover:shadow-lg transition-shadow duration-200' : '';

  return (
    <div
      className={`
        bg-white rounded-xl border border-gray-100
        ${paddingClasses[padding]}
        ${shadowClasses[shadow]}
        ${hoverClass}
        ${className}
      `}
      onClick={onClick}
    >
      {children}
    </div>
  );
};

interface CardHeaderProps {
  children: React.ReactNode;
  className?: string;
}

export const CardHeader: React.FC<CardHeaderProps> = ({
  children,
  className = '',
}) => (
  <div className={`border-b border-gray-100 pb-4 mb-6 ${className}`}>
    {children}
  </div>
);

interface CardTitleProps {
  children: React.ReactNode;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export const CardTitle: React.FC<CardTitleProps> = ({
  children,
  className = '',
  size = 'md',
}) => {
  const sizeClasses = {
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-2xl',
  };

  return (
    <h2 className={`font-bold text-gray-900 ${sizeClasses[size]} ${className}`}>
      {children}
    </h2>
  );
};

interface CardContentProps {
  children: React.ReactNode;
  className?: string;
}

export const CardContent: React.FC<CardContentProps> = ({
  children,
  className = '',
}) => (
  <div className={className}>
    {children}
  </div>
);

interface CardFooterProps {
  children: React.ReactNode;
  className?: string;
}

export const CardFooter: React.FC<CardFooterProps> = ({
  children,
  className = '',
}) => (
  <div className={`border-t border-gray-100 pt-4 mt-6 ${className}`}>
    {children}
  </div>
);

// Stats Card Component
interface StatsCardProps {
  title: string;
  value: string | number;
  icon?: React.ReactNode;
  trend?: {
    value: string;
    isPositive: boolean;
  };
  color?: 'blue' | 'green' | 'purple' | 'orange' | 'red';
  onClick?: () => void;
  clickable?: boolean;
}

export const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  icon,
  trend,
  color = 'blue',
  onClick,
  clickable = false,
}) => {
  const colorClasses = {
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    purple: 'bg-purple-500',
    orange: 'bg-orange-500',
    red: 'bg-red-500',
  };

  const cardContent = (
    <div className="flex items-center justify-between">
      <div className="flex-1">
        <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
        <p className="text-2xl font-bold text-gray-900">
          {typeof value === 'number' ? value.toLocaleString() : value}
        </p>
        {trend && (
          <p className={`text-sm mt-2 flex items-center ${
            trend.isPositive ? 'text-green-600' : 'text-red-600'
          }`}>
            <span className="mr-1">
              {trend.isPositive ? '↗' : '↘'}
            </span>
            {trend.value}
          </p>
        )}
      </div>
      {icon && (
        <div className={`p-3 rounded-lg ${colorClasses[color]}`}>
          <div className="text-white">
            {icon}
          </div>
        </div>
      )}
    </div>
  );

  if (clickable && onClick) {
    return (
      <Card
        hover
        className={`relative overflow-hidden cursor-pointer transform hover:scale-105 transition-all duration-200 ${
          clickable ? 'hover:shadow-lg' : ''
        }`}
        onClick={onClick}
      >
        {cardContent}
      </Card>
    );
  }

  return (
    <Card hover className="relative overflow-hidden">
      {cardContent}
    </Card>
  );
};

// Action Card Component
interface ActionCardProps {
  title: string;
  description: string;
  icon?: React.ReactNode;
  onClick?: () => void;
  href?: string;
  color?: 'blue' | 'green' | 'purple' | 'indigo' | 'orange' | 'red';
}

export const ActionCard: React.FC<ActionCardProps> = ({
  title,
  description,
  icon,
  onClick,
  href,
  color = 'blue',
}) => {
  const colorClasses = {
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    purple: 'bg-purple-500',
    indigo: 'bg-indigo-500',
    orange: 'bg-orange-500',
    red: 'bg-red-500',
  };

  const CardComponent = ({ children }: { children: React.ReactNode }) => (
    <Card hover className="cursor-pointer transform hover:scale-105 transition-transform duration-200">
      {children}
    </Card>
  );

  const content = (
    <div className="flex items-start space-x-4">
      {icon && (
        <div className={`p-3 rounded-lg ${colorClasses[color]} flex-shrink-0`}>
          <div className="text-white">
            {icon}
          </div>
        </div>
      )}
      <div className="flex-1">
        <h3 className="font-semibold text-gray-900 mb-1">{title}</h3>
        <p className="text-sm text-gray-600">{description}</p>
      </div>
    </div>
  );

  if (href) {
    return (
      <a href={href}>
        <CardComponent>{content}</CardComponent>
      </a>
    );
  }

  return (
    <div onClick={onClick}>
      <CardComponent>{content}</CardComponent>
    </div>
  );
};
