"use client";
import { useState, useEffect } from "react";
import { Card, StatsCard, ActionCard } from "@/components/ui/Card";
import adminApiRequest, { DashboardStats, RecentActivity } from "@/apiRequests/admin";
import { toast } from "react-toastify";
import PendingPostsModal from "@/components/Modal/PendingPostsModal";
import {
  Users,
  FileText,
  Calendar,
  Settings,
  TrendingUp,
  Eye,
  Plus,
  BarChart,
  Activity,
  Shield,
  Home,
  Globe
} from "react-feather";

// Helper functions
const getTimeAgo = (dateString: string) => {
  const now = new Date();
  const date = new Date(dateString);
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) return `${diffInSeconds} giây trước`;
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} phút trước`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} giờ trước`;
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} ngày trước`;
  return `${Math.floor(diffInSeconds / 2592000)} tháng trước`;
};

const getActivityType = (type: string) => {
  switch (type) {
    case 'post_created':
      return { color: 'bg-green-500', icon: 'create' };
    case 'post_updated':
      return { color: 'bg-blue-500', icon: 'update' };
    case 'post_pending':
      return { color: 'bg-orange-500', icon: 'pending' };
    case 'post_pending_updated':
      return { color: 'bg-orange-400', icon: 'pending' };
    case 'user_registered':
      return { color: 'bg-purple-500', icon: 'user' };
    case 'page_created':
      return { color: 'bg-green-500', icon: 'create' };
    case 'page_updated':
      return { color: 'bg-blue-500', icon: 'update' };
    default:
      return { color: 'bg-gray-500', icon: 'default' };
  }
};

const AdminDashboard = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalPosts: 0,
    totalUsers: 0,
    totalPages: 0,
    totalViews: 0,
    pendingPosts: 0,
    activeUsers: 0,
    analytics: {
      totalPageViews: 0,
      homeViewsToday: 0,
      postViewsToday: 0,
      totalViewsToday: 0,
      weeklyViews: 0,
      uniqueVisitorsToday: 0,
      homeViewsGrowth: 0,
      postViewsGrowth: 0
    }
  });
  const [loading, setLoading] = useState(true);
  const [sessionToken, setSessionToken] = useState<string>("");
  const [hasFetched, setHasFetched] = useState(false);
  const [activities, setActivities] = useState<RecentActivity[]>([]);
  const [activitiesLoading, setActivitiesLoading] = useState(true);
  const [showPendingModal, setShowPendingModal] = useState(false);

  useEffect(() => {
    // Get session token on client side only
    const token = localStorage.getItem("sessionToken") || "";
    setSessionToken(token);
  }, []);

  useEffect(() => {
    if (!sessionToken || hasFetched) return; // Wait for session token and prevent multiple fetches

    const fetchDashboardStats = async () => {
      try {
        setLoading(true);
        console.log("Fetching dashboard stats...");

        const result = await adminApiRequest.getDashboardStats(sessionToken);
        console.log("API Response:", result);

        if (result.payload.success) {
          console.log("Dashboard stats received:", result.payload.stats);
          setStats(result.payload.stats);
          setHasFetched(true);
          toast.success("Đã tải thống kê thành công");
        } else {
          toast.error("Không thể tải thống kê dashboard");
        }
      } catch (error: any) {
        console.error("Error fetching dashboard stats:", error);

        // Use fallback data on error
        setStats({
          totalPosts: 245,
          totalUsers: 156,
          totalPages: 34,
          totalViews: 28450,
          pendingPosts: 12,
          activeUsers: 89,
          analytics: {
            totalPageViews: 15000,
            homeViewsToday: 120,
            postViewsToday: 340,
            totalViewsToday: 460,
            weeklyViews: 3200,
            uniqueVisitorsToday: 85,
            homeViewsGrowth: 12.5,
            postViewsGrowth: 8.3
          }
        });
        setHasFetched(true);

        if (error?.status === 429) {
          toast.warning("Quá nhiều yêu cầu, sử dụng dữ liệu cache");
        } else {
          toast.warning("Lỗi API, sử dụng dữ liệu mẫu");
        }
      } finally {
        setLoading(false);
      }
    };

    // Add delay to prevent immediate multiple calls
    const timeoutId = setTimeout(fetchDashboardStats, 1000);
    return () => clearTimeout(timeoutId);
  }, [sessionToken]); // Remove hasFetched from dependencies to prevent loops

  // Fetch recent activities
  useEffect(() => {
    if (!sessionToken) return;

    const fetchRecentActivities = async () => {
      try {
        setActivitiesLoading(true);
        const result = await adminApiRequest.getRecentActivities(sessionToken, 8);
        if (result.payload.success) {
          setActivities(result.payload.activities);
        } else {
          toast.error("Không thể tải hoạt động gần đây");
        }
      } catch (error: any) {
        console.error("Error fetching recent activities:", error);
        if (error?.status === 429) {
          toast.warning("Quá nhiều yêu cầu, vui lòng thử lại sau");
        } else {
          toast.error("Lỗi khi tải hoạt động gần đây");
        }
      } finally {
        setActivitiesLoading(false);
      }
    };

    const timeoutId = setTimeout(fetchRecentActivities, 1500);
    return () => clearTimeout(timeoutId);
  }, [sessionToken]);

  const quickActions = [
    {
      title: "Thêm bài viết mới",
      description: "Tạo bài viết mới cho website",
      href: "/dashboard/secret/blog/add",
      icon: <Plus size={20} />,
      color: "blue" as const,
    },
    {
      title: "Quản lý lãnh đạo",
      description: "Quản lý thông tin lãnh đạo tòa án",
      href: "/dashboard/secret/leaders",
      icon: <Users size={20} />,
      color: "purple" as const,
    },
    {
      title: "Quản lý chức vụ",
      description: "Quản lý các chức vụ và quyền hạn",
      href: "/dashboard/secret/positions",
      icon: <Shield size={20} />,
      color: "indigo" as const,
    },
    {
      title: "Quản lý thành viên",
      description: "Xem và quản lý tài khoản người dùng",
      href: "/dashboard/secret/user",
      icon: <Users size={20} />,
      color: "green" as const,
    },
    {
      title: "Quản lý danh mục",
      description: "Tổ chức và phân loại nội dung",
      href: "/dashboard/secret/categories",
      icon: <FileText size={20} />,
      color: "purple" as const,
    },
    {
      title: "Cài đặt hệ thống",
      description: "Cấu hình và tùy chỉnh website",
      href: "/dashboard/secret/setting",
      icon: <Settings size={20} />,
      color: "orange" as const,
    },
    {
      title: "Quản lý trang",
      description: "Tạo và chỉnh sửa các trang tĩnh",
      href: "/dashboard/secret/page",
      icon: <Calendar size={20} />,
      color: "red" as const,
    },
    {
      title: "Quản lý menu",
      description: "Cấu hình menu điều hướng",
      href: "/dashboard/secret/menu",
      icon: <BarChart size={20} />,
      color: "green" as const,
    },
  ];

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl text-white p-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2 flex items-center">
              <Shield className="mr-3" size={36} />
              Bảng điều khiển quản trị
            </h1>
            <p className="text-purple-100 text-lg">
              Quản lý toàn bộ hệ thống và nội dung website
            </p>
          </div>
          <div className="hidden md:block">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
              <Activity size={48} className="text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
        {loading ? (
          // Loading skeleton
          Array.from({ length: 6 }).map((_, index) => (
            <div key={index} className="bg-white rounded-xl p-6 shadow-sm border animate-pulse">
              <div className="flex items-center justify-between">
                <div>
                  <div className="h-4 bg-gray-200 rounded w-20 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-16"></div>
                </div>
                <div className="w-12 h-12 bg-gray-200 rounded-lg"></div>
              </div>
            </div>
          ))
        ) : (
          <>
            <StatsCard
              title="Tổng bài viết"
              value={stats.totalPosts}
              icon={<FileText size={24} />}
              color="blue"
            />
            <StatsCard
              title="Thành viên"
              value={stats.totalUsers}
              icon={<Users size={24} />}
              color="green"
            />
            <StatsCard
              title="Trang"
              value={stats.totalPages}
              icon={<Calendar size={24} />}
              color="purple"
            />
            <StatsCard
              title="Lượt xem"
              value={stats.totalViews}
              icon={<Eye size={24} />}
              color="orange"
            />
            <StatsCard
              title="Chờ kích hoạt"
              value={stats.pendingPosts}
              icon={<Calendar size={24} />}
              color="orange"
              clickable={true}
              onClick={() => {
                console.log('Pending posts clicked:', stats.pendingPosts);
                console.log('Stats object:', stats);
                console.log('Clickable condition:', stats.pendingPosts > 0);
                if (stats.pendingPosts > 0) {
                  console.log('Opening pending posts modal');
                  setShowPendingModal(true);
                } else {
                  console.log('No pending posts to show - forcing modal open for debug');
                  setShowPendingModal(true); // Force open for debug
                }
              }}
            />
            <StatsCard
              title="Đang hoạt động"
              value={stats.activeUsers}
              icon={<Activity size={24} />}
              color="green"
            />
          </>
        )}
      </div>

      {/* Analytics Section */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Thống kê truy cập</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {loading ? (
            // Loading skeleton for analytics
            Array.from({ length: 4 }).map((_, index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-sm border animate-pulse">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
                    <div className="h-8 bg-gray-200 rounded w-16"></div>
                    <div className="h-3 bg-gray-200 rounded w-20 mt-2"></div>
                  </div>
                  <div className="w-12 h-12 bg-gray-200 rounded-lg"></div>
                </div>
              </div>
            ))
          ) : (
            <>
              <StatsCard
                title="Lượt xem trang chủ hôm nay"
                value={stats.analytics.homeViewsToday}
                icon={<Home size={24} />}
                color="blue"
                trend={{
                  value: `${stats.analytics.homeViewsGrowth > 0 ? '+' : ''}${stats.analytics.homeViewsGrowth}%`,
                  isPositive: stats.analytics.homeViewsGrowth >= 0
                }}
              />
              <StatsCard
                title="Lượt xem bài viết hôm nay"
                value={stats.analytics.postViewsToday}
                icon={<FileText size={24} />}
                color="green"
                trend={{
                  value: `${stats.analytics.postViewsGrowth > 0 ? '+' : ''}${stats.analytics.postViewsGrowth}%`,
                  isPositive: stats.analytics.postViewsGrowth >= 0
                }}
              />
              <StatsCard
                title="Tổng lượt xem hôm nay"
                value={stats.analytics.totalViewsToday}
                icon={<Eye size={24} />}
                color="purple"
              />
              <StatsCard
                title="Khách truy cập duy nhất"
                value={stats.analytics.uniqueVisitorsToday}
                icon={<Users size={24} />}
                color="orange"
              />
            </>
          )}
        </div>

        {/* Weekly Stats */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Thống kê tuần này</h3>
              <Globe size={20} className="text-gray-500" />
            </div>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Tổng lượt xem tuần</span>
                <span className="font-semibold text-lg">{stats.analytics.weeklyViews.toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Tổng lượt xem tất cả</span>
                <span className="font-semibold text-lg">{stats.analytics.totalPageViews.toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Trung bình/ngày</span>
                <span className="font-semibold text-lg">{Math.round(stats.analytics.weeklyViews / 7).toLocaleString()}</span>
              </div>
            </div>
          </Card>

          <Card>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Hiệu suất hôm nay</h3>
              <TrendingUp size={20} className="text-gray-500" />
            </div>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Trang chủ</span>
                <div className="flex items-center space-x-2">
                  <span className="font-semibold">{stats.analytics.homeViewsToday}</span>
                  <span className={`text-sm ${stats.analytics.homeViewsGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {stats.analytics.homeViewsGrowth > 0 ? '+' : ''}{stats.analytics.homeViewsGrowth}%
                  </span>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Bài viết</span>
                <div className="flex items-center space-x-2">
                  <span className="font-semibold">{stats.analytics.postViewsToday}</span>
                  <span className={`text-sm ${stats.analytics.postViewsGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {stats.analytics.postViewsGrowth > 0 ? '+' : ''}{stats.analytics.postViewsGrowth}%
                  </span>
                </div>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Tỷ lệ tương tác</span>
                <span className="font-semibold">
                  {stats.analytics.totalViewsToday > 0
                    ? Math.round((stats.analytics.uniqueVisitorsToday / stats.analytics.totalViewsToday) * 100)
                    : 0}%
                </span>
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* Quick Actions */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Thao tác nhanh</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {quickActions.map((action, index) => (
            <ActionCard
              key={index}
              title={action.title}
              description={action.description}
              href={action.href}
              icon={action.icon}
              color={action.color}
            />
          ))}
        </div>
      </div>

      {/* Recent Activity */}
      <Card>
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Hoạt động gần đây</h3>
          <a href="#" className="text-blue-600 hover:text-blue-700 text-sm font-medium">
            Xem tất cả
          </a>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {activitiesLoading ? (
            // Loading skeleton
            Array.from({ length: 8 }).map((_, index) => (
              <div key={index} className="flex items-center space-x-3 p-3 rounded-lg border border-gray-100 animate-pulse">
                <div className="w-2 h-2 rounded-full bg-gray-200"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-1"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))
          ) : activities.length > 0 ? (
            activities.map((activity) => {
              const timeAgo = getTimeAgo(activity.time);
              const activityType = getActivityType(activity.type);

              return (
                <div key={activity.id} className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 border border-gray-100">
                  <div className={`w-2 h-2 rounded-full ${activityType.color}`}></div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-900">{activity.action}</p>
                    <p className="text-xs text-gray-500">
                      {activity.title && (
                        <span className="font-medium">{activity.title}</span>
                      )}
                      {activity.title && " • "}
                      bởi {activity.user} • {timeAgo}
                    </p>
                  </div>
                </div>
              );
            })
          ) : (
            <div className="col-span-full text-center py-8 text-gray-500">
              <Activity size={48} className="mx-auto mb-2 opacity-50" />
              <p>Chưa có hoạt động nào</p>
            </div>
          )}
        </div>
      </Card>

      {/* Pending Posts Modal */}
      <PendingPostsModal
        isOpen={showPendingModal}
        onClose={() => setShowPendingModal(false)}
        sessionToken={sessionToken}
      />
    </div>
  );
};

export default AdminDashboard;
