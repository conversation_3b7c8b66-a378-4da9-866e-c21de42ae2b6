"use client";
import { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import {
  Users,
  Phone,
  Mail,
  Award,
  BookOpen,
  Clock,
  Shield,
  User,
  ChevronLeft,
  ChevronRight,
  Search,
  Filter
} from "react-feather";
import { getLeaderImageUrl } from "@/utils/imageUtils";

interface Leader {
  id: string;
  name: string;
  position: string | {
    _id: string;
    name: string;
    description?: string;
    level: number;
  };
  department: string | {
    _id: string;
    name: string;
  };
  bio?: string;
  experience?: string;
  education?: string;
  achievements: string[];
  phone?: string;
  email?: string;
  image?: string;
  order: number;
  isActive: boolean;
}

interface DepartmentPyramid {
  departmentName: string;
  departmentId: string;
  departmentLevel: number;
  levels: {
    level: number;
    leaders: Leader[];
  }[];
}

interface PositionCategory {
  title: string;
  leaders: Leader[];
  color: string;
  icon: React.ReactNode;
}

const CourtLeadersPage = () => {
  const [leaders, setLeaders] = useState<Leader[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedDepartment, setSelectedDepartment] = useState<string>("all");
  const [selectedPosition, setSelectedPosition] = useState<string>("all");
  const [selectedLeader, setSelectedLeader] = useState<Leader | null>(null);
  const [showModal, setShowModal] = useState(false);
  useEffect(() => {
    fetchLeaders();
    
    // Add entrance animations
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add('animate-fade-in-up');
          }
        });
      },
      { threshold: 0.1 }
    );

    // Add CSS for animations
    if (typeof document !== 'undefined') {
      const style = document.createElement('style');
      style.textContent = `
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        .animate-fade-in-up {
          animation: fadeInUp 0.6s ease-out forwards;
        }
        .leader-card {
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .leader-card:hover {
          transform: translateY(-8px) scale(1.02);
        }
        .pulse-ring {
          animation: pulse-ring 2s cubic-bezier(0.455, 0.03, 0.515, 0.955) infinite;
        }
        @keyframes pulse-ring {
          0% {
            transform: scale(0.8);
            opacity: 1;
          }
          80%, 100% {
            transform: scale(1.2);
            opacity: 0;
          }
        }
        .floating {
          animation: floating 3s ease-in-out infinite;
        }
        @keyframes floating {
          0%, 100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-10px);
          }
        }

        /* Pyramid Department Styles */
        .pyramid-container {
          perspective: 1000px;
        }
        .pyramid-level {
          transform-style: preserve-3d;
          transition: all 0.3s ease;
        }
        .pyramid-level:hover {
          transform: translateZ(10px);
        }
        .leader-card-pyramid {
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          backdrop-filter: blur(10px);
        }
        .leader-card-pyramid:hover {
          transform: translateY(-5px) scale(1.02);
          box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .department-header {
          background: linear-gradient(135deg, #e4393c 0%, #dc2626 50%, #b91c1c 100%);
        }
        .level-indicator {
          animation: pulse 2s infinite;
        }
        @keyframes pulse {
          0%, 100% { opacity: 0.6; }
          50% { opacity: 1; }
        }

        /* Responsive Pyramid Margins */
        @media (max-width: 768px) {
          .pyramid-level {
            margin-left: var(--margin-mobile) !important;
            margin-right: var(--margin-mobile) !important;
          }
        }

        /* Enhanced Pyramid Visual Effects */
        .pyramid-level {
          position: relative;
        }
        .pyramid-level::before {
          content: '';
          position: absolute;
          top: -10px;
          left: 50%;
          transform: translateX(-50%);
          width: 2px;
          height: 20px;
          background: linear-gradient(to bottom, transparent, #e4393c);
          opacity: 0.3;
        }
        .pyramid-level:first-child::before {
          display: none;
        }
      `;
      document.head.appendChild(style);
    }

    return () => observer.disconnect();
  }, []);  const fetchLeaders = async () => {
    try {
      setLoading(true);

      const response = await fetch("/api/leaders/public");
      const result = await response.json();

      if (response.ok) {
        console.log('Leaders fetched successfully:', result.data?.length || 0, 'leaders');
        console.log('Leaders data:', result.data);
        setLeaders(result.data || []);
      } else {
        console.error("Error fetching leaders:", result.message);
        setLeaders([]);
      }
    } catch (error) {
      console.error("Error fetching leaders:", error);
      setLeaders([]);
    } finally {
      setLoading(false);
    }
  };
  // Helper functions to get position and department names
  const getPositionName = (position: Leader['position']): string => {
    return typeof position === 'string' ? position : position?.name || '';
  };

  const getDepartmentName = (department: Leader['department']): string => {
    return typeof department === 'string' ? department : department?.name || '';
  };

  const getDepartmentId = (department: Leader['department']): string => {
    return typeof department === 'string' ? department : department?._id || '';
  };

  const getDepartmentLevel = (department: Leader['department']): number => {
    return typeof department === 'string' ? 5 : department?.level || 5; // Default level 5 for string departments
  };

  const getPositionLevel = (position: Leader['position']): number => {
    return typeof position === 'string' ? 1 : position?.level || 1;
  };

  const getPositionIcon = (position: Leader['position']) => {
    const positionName = getPositionName(position);
    if (positionName.includes("Chánh án")) return <Shield size={24} className="text-red-600" />;
    if (positionName.includes("Phó")) return <Award size={24} className="text-[#e4393c]" />;
    return <User size={24} className="text-gray-600" />;
  };

  const getPositionColor = (position: Leader['position']) => {
    const positionName = getPositionName(position);
    if (positionName.includes("Chánh án")) return "bg-red-100 text-red-800 border-red-200";
    if (positionName.includes("Phó")) return "bg-[#e4393c] bg-opacity-10 text-[#e4393c] border-red-200";
    if (positionName.includes("Thẩm phán")) return "bg-green-100 text-green-800 border-green-200";
    return "bg-gray-100 text-gray-800 border-gray-200";
  };

  const filteredLeaders = leaders.filter(leader => {
    const positionName = getPositionName(leader.position);
    const departmentName = getDepartmentName(leader.department);

    const matchesSearch = leader.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         positionName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         departmentName.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesDepartment = selectedDepartment === "all" || departmentName === selectedDepartment;
    const matchesPosition = selectedPosition === "all" || positionName === selectedPosition;

    return matchesSearch && matchesDepartment && matchesPosition && leader.isActive;
  });

  console.log('Filtered leaders:', filteredLeaders.length, 'out of', leaders.length);

  // Create pyramid structure grouped by department and position level
  const createDepartmentPyramids = (): DepartmentPyramid[] => {
    // Group leaders by department with level information
    const departmentGroups = new Map<string, { leaders: Leader[], level: number }>();

    filteredLeaders.forEach(leader => {
      const deptName = getDepartmentName(leader.department);
      const deptId = getDepartmentId(leader.department);
      const deptLevel = getDepartmentLevel(leader.department);
      const key = `${deptId}|${deptName}`;

      if (!departmentGroups.has(key)) {
        departmentGroups.set(key, { leaders: [], level: deptLevel });
      }
      departmentGroups.get(key)!.leaders.push(leader);
    });

    // Create pyramid structure for each department
    const pyramids: DepartmentPyramid[] = [];

    departmentGroups.forEach(({ leaders, level: deptLevel }, key) => {
      const [deptId, deptName] = key.split('|');

      // Group leaders by position level within department
      const levelGroups = new Map<number, Leader[]>();

      leaders.forEach(leader => {
        const level = getPositionLevel(leader.position);
        if (!levelGroups.has(level)) {
          levelGroups.set(level, []);
        }
        levelGroups.get(level)!.push(leader);
      });

      // Convert to sorted levels array (LOWEST level first - reverse sorting)
      const levels = Array.from(levelGroups.entries())
        .map(([level, leaders]) => ({
          level,
          leaders: leaders.sort((a, b) => a.order - b.order) // Sort by order within same level
        }))
        .sort((a, b) => a.level - b.level); // Sort levels ASCENDING (lowest first)

      pyramids.push({
        departmentName: deptName,
        departmentId: deptId,
        levels,
        departmentLevel: deptLevel // Add department level for sorting
      });
    });

    // Sort departments by level (lowest level first), then by name
    return pyramids.sort((a, b) => {
      if (a.departmentLevel !== b.departmentLevel) {
        return a.departmentLevel - b.departmentLevel; // Lower level numbers first
      }
      return a.departmentName.localeCompare(b.departmentName);
    });
  };

  const departmentPyramids = createDepartmentPyramids();
  console.log('Department pyramids created:', departmentPyramids.length, 'departments');

  // Helper functions for pyramid styling
  const getLevelColor = (level: number): string => {
    const colors = {
      10: "bg-gradient-to-br from-red-50 to-red-100 border-red-200",
      9: "bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200",
      8: "bg-gradient-to-br from-amber-50 to-amber-100 border-amber-200",
      7: "bg-gradient-to-br from-yellow-50 to-yellow-100 border-yellow-200",
      6: "bg-gradient-to-br from-green-50 to-green-100 border-green-200",
      5: "bg-gradient-to-br from-emerald-50 to-emerald-100 border-emerald-200",
      4: "bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200",
      3: "bg-gradient-to-br from-indigo-50 to-indigo-100 border-indigo-200",
      2: "bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200",
      1: "bg-gradient-to-br from-gray-50 to-gray-100 border-gray-200"
    };
    return colors[level as keyof typeof colors] || colors[1];
  };

  const getLevelIcon = (level: number) => {
    if (level >= 8) return <Shield size={20} className="text-red-600" />;
    if (level >= 6) return <Award size={20} className="text-orange-600" />;
    if (level >= 4) return <BookOpen size={20} className="text-green-600" />;
    if (level >= 2) return <User size={20} className="text-blue-600" />;
    return <Users size={20} className="text-gray-600" />;
  };

  const getPyramidLevelStyle = (level: number, maxLevel: number): string => {
    const levelFromTop = maxLevel - level + 1;
    const marginMultiplier = (levelFromTop - 1) * 2;
    const scaleValue = 100 - (levelFromTop - 1) * 3;

    return `mx-${marginMultiplier} transform scale-${scaleValue}`;
  };

  const departments = [...new Set(leaders.map(l => getDepartmentName(l.department)))];
  const positions = [...new Set(leaders.map(l => getPositionName(l.position)))];

  const openModal = (leader: Leader) => {
    console.log('Opening modal for leader:', leader.name);
    setSelectedLeader(leader);
    setShowModal(true);
    document.body.style.overflow = 'hidden';
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedLeader(null);
    document.body.style.overflow = 'unset';
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-10">
        <div className="bg-white rounded-xl shadow-lg border-0 p-6 mb-8 backdrop-blur-sm">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Tìm kiếm lãnh đạo</h2>
              <p className="text-gray-600">Sử dụng bộ lọc để tìm kiếm thông tin lãnh đạo</p>
            </div>
            <div className="hidden md:flex items-center space-x-2 text-sm text-gray-500">
              <span className="bg-[#e4393c] bg-opacity-10 text-[#e4393c] px-3 py-1 rounded-full font-medium">
                {filteredLeaders.length} kết quả
              </span>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Tìm kiếm theo tên, chức vụ..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#e4393c] focus:border-transparent transition-all"
              />
            </div>
            
            <div className="relative">
              <Filter size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />              <select
                value={selectedDepartment}
                onChange={(e) => setSelectedDepartment(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#e4393c] focus:border-transparent appearance-none transition-all"
              >
                <option value="all">Tất cả phòng ban</option>
                {departments.map(dept => (
                  <option key={dept} value={dept}>{dept}</option>
                ))}
              </select>
            </div>
            
            <div className="relative">
              <Filter size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />              <select
                value={selectedPosition}
                onChange={(e) => setSelectedPosition(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#e4393c] focus:border-transparent appearance-none transition-all"
              >
                <option value="all">Tất cả chức vụ</option>
                {positions.map(pos => (
                  <option key={pos} value={pos}>{pos}</option>
                ))}
              </select>
            </div>
          </div>
          
          {(searchTerm || selectedDepartment !== "all" || selectedPosition !== "all") && (
            <div className="mt-6 pt-4 border-t border-gray-100">
              <div className="flex items-center justify-between">                <p className="text-sm text-gray-600">
                  Tìm thấy <span className="font-semibold text-[#e4393c]">{filteredLeaders.length}</span> kết quả
                  {searchTerm && (
                    <span> cho "<strong className="text-gray-900">{searchTerm}</strong>"</span>
                  )}
                </p>
                <button
                  onClick={() => {
                    setSearchTerm("");
                    setSelectedDepartment("all");
                    setSelectedPosition("all");
                  }}
                  className="text-sm text-[#e4393c] hover:text-red-800 font-medium transition-colors"
                >
                  Xóa bộ lọc
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Leaders by Categories */}
        {loading ? (
          <div className="space-y-8">
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="bg-white rounded-lg shadow-sm border p-6">
                <div className="h-6 bg-gray-200 rounded w-1/4 mb-6 animate-pulse"></div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <div key={i} className="animate-pulse">
                      <div className="flex items-center space-x-4 p-4 border rounded-lg">
                        <div className="w-16 h-16 bg-gray-200 rounded-lg"></div>
                        <div className="flex-1">
                          <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                          <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
                          <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        ) : filteredLeaders.length === 0 ? (            <div className="bg-white rounded-xl shadow-lg border p-12 text-center">
              <div className="bg-gray-50 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
                <Users size={40} className="text-gray-400" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Không tìm thấy kết quả
              </h3>
              <p className="text-gray-500 mb-8 max-w-md mx-auto">
                Thử thay đổi từ khóa tìm kiếm hoặc bộ lọc để xem thêm kết quả phù hợp
              </p>              <button
                onClick={() => {
                  setSearchTerm("");
                  setSelectedDepartment("all");
                  setSelectedPosition("all");
                }}
                className="bg-[#e4393c] hover:bg-red-700 text-white px-8 py-3 rounded-xl transition-all transform hover:scale-105 font-medium shadow-lg"
              >
                Xóa tất cả bộ lọc
              </button>
            </div>
        ) : (
          <div className="space-y-12">
            {departmentPyramids.map((pyramid, deptIndex) => (
              <div key={pyramid.departmentId} className="bg-white rounded-2xl shadow-xl border-0 overflow-hidden">
                {/* Department Header */}
                <div className="bg-gradient-to-r from-[#e4393c] to-red-600 p-8 text-white relative overflow-hidden">
                  <div className="absolute inset-0 bg-black/10"></div>
                  <div className="absolute top-0 right-0 w-32 h-32 bg-white/5 rounded-full -mr-16 -mt-16"></div>
                  <div className="relative">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="bg-white/20 backdrop-blur-sm rounded-xl p-4 shadow-lg">
                          <Users size={32} className="text-white" />
                        </div>
                        <div>
                          <h2 className="text-3xl font-bold mb-2">{pyramid.departmentName}</h2>
                          <p className="text-red-100 text-lg">Cơ cấu tổ chức phòng ban</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="bg-white/20 backdrop-blur-sm rounded-xl px-6 py-3 shadow-lg">
                          <span className="text-3xl font-bold text-white">
                            {pyramid.levels.reduce((total, level) => total + level.leaders.length, 0)}
                          </span>
                          <p className="text-red-100 text-sm mt-1">Lãnh đạo</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Pyramid Structure */}
                <div className="p-4 md:p-8 space-y-6 md:space-y-8 pyramid-container">
                  {pyramid.levels.map((levelData, levelIndex) => {
                    const minLevel = Math.min(...pyramid.levels.map(l => l.level));
                    // Reverse pyramid: lower levels (numerically) have smaller margins, higher levels have larger margins
                    const marginMultiplier = (levelData.level - minLevel) * 1.5; // Adjusted for reverse pyramid
                    const marginValue = Math.max(0, marginMultiplier * 20); // 20px per step for desktop
                    const marginValueMobile = Math.max(0, marginMultiplier * 8); // 8px per step for mobile

                    return (
                      <div
                        key={levelData.level}
                        className="transform transition-all hover:scale-105 pyramid-level"
                        style={{
                          marginLeft: `${marginValue}px`,
                          marginRight: `${marginValue}px`,
                          animationDelay: `${levelIndex * 200}ms`,
                          // Responsive margins using CSS custom properties
                          '--margin-mobile': `${marginValueMobile}px`
                        }}
                      >
                        {/* Level Header */}
                        <div className={`${getLevelColor(levelData.level)} rounded-xl p-4 mb-6 border-2 shadow-lg`}>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <div className="bg-white/70 backdrop-blur-sm rounded-lg p-3 shadow-md">
                                {getLevelIcon(levelData.level)}
                              </div>
                              <div>
                                <h3 className="text-xl font-bold text-gray-900 mb-1">
                                  Cấp {levelData.level}
                                </h3>
                                <p className="text-sm text-gray-600 font-medium">
                                  {levelData.leaders.length} {levelData.leaders.length === 1 ? 'người' : 'người'}
                                </p>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              <div className="flex space-x-1">
                                {Array.from({ length: levelData.level }, (_, i) => (
                                  <div
                                    key={i}
                                    className="w-2 h-2 bg-gray-500 rounded-full level-indicator"
                                    style={{ animationDelay: `${i * 100}ms` }}
                                  ></div>
                                ))}
                              </div>
                              <div className="text-xs text-gray-500 font-medium ml-2">
                                Level {levelData.level}
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Leaders in this level - arranged horizontally */}
                        <div className="flex flex-wrap justify-center gap-4 px-4">
                          {levelData.leaders.map((leader, leaderIndex) => (
                            <div
                              key={leader.id}
                              className="bg-gradient-to-br from-white to-gray-50 border border-gray-200 rounded-xl p-4 hover:shadow-xl hover:border-[#e4393c] transition-all duration-300 cursor-pointer group transform hover:-translate-y-1 leader-card-pyramid flex-shrink-0"
                              onMouseDown={() => {
                                console.log('Mouse down on:', leader.name);
                                alert('Mouse down: ' + leader.name);
                              }}
                              onClick={(e) => {
                                console.log('Leader card clicked:', leader.name);
                                console.log('Click event triggered!');
                                alert('Card clicked: ' + leader.name);
                                e.preventDefault();
                                e.stopPropagation();
                                openModal(leader);
                              }}
                              style={{
                                animationDelay: `${leaderIndex * 100}ms`,
                                minWidth: '260px',
                                maxWidth: '300px',
                                flex: '0 0 auto',
                                pointerEvents: 'auto',
                                zIndex: 10
                              }}
                            >
                              {/* Leader Avatar and Info */}
                              <div className="text-center mb-4">
                                <div className="relative mx-auto mb-3">
                                  <div className="w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center overflow-hidden mx-auto ring-4 ring-white shadow-lg">
                                    {leader.image ? (
                                      <img
                                        src={getLeaderImageUrl(leader.image)}
                                        alt={leader.name}
                                        className="w-full h-full object-cover"
                                      />
                                    ) : (
                                      <User size={28} className="text-gray-400" />
                                    )}
                                  </div>
                                </div>
                                <h3 className="font-bold text-gray-900 group-hover:text-[#e4393c] transition-colors text-lg mb-2">
                                  {leader.name}
                                </h3>
                                <div className="flex items-center justify-center space-x-2 mb-2">
                                  <div className="w-4 h-4 flex items-center justify-center">
                                    {getPositionIcon(leader.position)}
                                  </div>
                                  <span className={`px-3 py-1 rounded-full text-xs font-semibold border ${getPositionColor(leader.position)}`}>
                                    {getPositionName(leader.position)}
                                  </span>
                                </div>
                              </div>                              {/* Compact Info */}
                              <div className="space-y-2 text-sm text-gray-600 mb-4">
                                {leader.experience && (
                                  <div className="flex items-center justify-center space-x-2 bg-red-50 p-2 rounded-lg">
                                    <Clock size={12} className="text-[#e4393c]" />
                                    <span className="font-medium text-xs">{leader.experience}</span>
                                  </div>
                                )}
                                {leader.education && (
                                  <div className="flex items-center justify-center space-x-2 bg-green-50 p-2 rounded-lg">
                                    <BookOpen size={12} className="text-green-600" />
                                    <span className="font-medium text-xs line-clamp-1">{leader.education}</span>
                                  </div>
                                )}
                              </div>

                              <div className="pt-3 border-t border-gray-100 flex items-center justify-between">
                                <div className="flex items-center space-x-1 text-xs text-gray-500">
                                  <Award size={10} className="text-yellow-600" />
                                  <span className="font-medium">{leader.achievements.length}</span>
                                </div>
                                <div className="flex items-center space-x-1 text-[#e4393c] group-hover:text-red-700 transition-colors">
                                  <span className="text-xs font-medium">Chi tiết</span>
                                  <ChevronRight size={12} className="transform group-hover:translate-x-1 transition-transform" />
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>      {/* Modal */}
      {showModal && selectedLeader && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div
              className="fixed inset-0 transition-opacity bg-black bg-opacity-60 backdrop-blur-sm"
              onClick={closeModal}
            ></div>

            <div className="inline-block w-full max-w-5xl my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-2xl rounded-2xl">
              {/* Modal Header */}
              <div className="bg-gradient-to-br from-[#e4393c] via-red-600 to-red-800 px-8 py-10 text-white relative overflow-hidden">
                <div className="absolute inset-0 bg-black/10"></div>
                <div className="absolute top-0 right-0 w-32 h-32 bg-white/5 rounded-full -mr-16 -mt-16"></div>
                <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/5 rounded-full -ml-12 -mb-12"></div>
                
                <div className="relative flex items-center justify-between">
                  <div className="flex items-center space-x-6">
                    <div className="relative">
                      <div className="w-24 h-24 bg-white/10 backdrop-blur-sm rounded-2xl flex items-center justify-center overflow-hidden ring-4 ring-white/20">
                        {selectedLeader.image ? (
                          <img
                            src={getLeaderImageUrl(selectedLeader.image)}
                            alt={selectedLeader.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <User size={40} className="text-white" />
                        )}
                      </div>
                    </div>
                    <div>
                      <h2 className="text-3xl font-bold mb-2">{selectedLeader.name}</h2>
                      <div className="flex items-center space-x-3 mb-2">
                        <div className="w-6 h-6 flex items-center justify-center">
                          {getPositionIcon(selectedLeader.position)}
                        </div>
                        <span className="bg-white/20 backdrop-blur-sm px-4 py-2 rounded-xl text-sm font-semibold">
                          {getPositionName(selectedLeader.position)}
                        </span>
                      </div>
                      <p className="text-blue-100 text-lg font-medium">{getDepartmentName(selectedLeader.department)}</p>
                    </div>
                  </div>                  <button
                    onClick={closeModal}
                    className="text-white hover:text-red-200 transition-colors bg-white/10 backdrop-blur-sm rounded-xl p-3 hover:bg-white/20"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>

              {/* Modal Content */}
              <div className="px-8 py-8 max-h-96 overflow-y-auto">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {/* Main Info */}
                  <div className="lg:col-span-2 space-y-8">
                    <div>
                      <div className="flex items-center space-x-2 mb-4">                        <div className="w-8 h-8 bg-[#e4393c] bg-opacity-10 rounded-lg flex items-center justify-center">
                          <User size={16} className="text-[#e4393c]" />
                        </div>
                        <h3 className="text-xl font-bold text-gray-900">Tiểu sử</h3>
                      </div>
                      <div className="bg-gray-50 rounded-xl p-6">
                        <p className="text-gray-700 leading-relaxed text-base">
                          {selectedLeader.bio || "Chưa có thông tin tiểu sử"}
                        </p>
                      </div>
                    </div>

                    {selectedLeader.achievements.length > 0 && (
                      <div>
                        <div className="flex items-center space-x-2 mb-4">                        <div className="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                          <Award size={16} className="text-yellow-600" />
                        </div>
                          <h3 className="text-xl font-bold text-gray-900">Thành tích & Giải thưởng</h3>
                        </div>
                        <div className="space-y-3">
                          {selectedLeader.achievements.map((achievement, index) => (
                            <div key={index} className="flex items-start space-x-4 bg-gradient-to-r from-yellow-50 to-orange-50 p-4 rounded-xl border border-yellow-200">
                              <div className="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5">
                                <Award size={16} className="text-white" />
                              </div>
                              <div>
                                <span className="text-gray-800 font-medium">{achievement}</span>
                                <div className="text-xs text-yellow-700 mt-1">Thành tích xuất sắc</div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Side Info */}
                  <div className="space-y-6">                    {(selectedLeader.experience || selectedLeader.education || selectedLeader.bio) && (
                      <div className="bg-gradient-to-br from-red-50 to-pink-50 p-6 rounded-xl border border-red-200">
                        <div className="flex items-center space-x-2 mb-4">
                          <div className="w-8 h-8 bg-[#e4393c] rounded-lg flex items-center justify-center">
                            <Clock size={16} className="text-white" />
                          </div>
                          <h3 className="text-lg font-bold text-gray-900">Thông tin chi tiết</h3>
                        </div>
                        <div className="space-y-4">
                          {selectedLeader.experience && (
                            <div className="bg-white p-4 rounded-lg border border-red-100">
                              <div className="flex items-center space-x-3">
                                <Clock size={18} className="text-[#e4393c]" />
                                <div>
                                  <p className="text-sm text-gray-500 font-medium">Kinh nghiệm</p>
                                  <p className="font-bold text-gray-900">{selectedLeader.experience}</p>
                                </div>
                              </div>
                            </div>
                          )}

                          {selectedLeader.education && (
                            <div className="bg-white p-4 rounded-lg border border-green-100">
                              <div className="flex items-center space-x-3">
                                <BookOpen size={18} className="text-green-600" />
                                <div>
                                  <p className="text-sm text-gray-500 font-medium">Học vấn</p>
                                  <p className="font-bold text-gray-900">{selectedLeader.education}</p>
                                </div>
                              </div>
                            </div>
                          )}

                          {selectedLeader.bio && (
                            <div className="bg-white p-4 rounded-lg border border-blue-100">
                              <div className="flex items-center space-x-3">
                                <User size={18} className="text-blue-600" />
                                <div>
                                  <p className="text-sm text-gray-500 font-medium">Tiểu sử</p>
                                  <p className="font-bold text-gray-900">{selectedLeader.bio}</p>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {(selectedLeader.phone || selectedLeader.email) && (                      <div className="bg-gradient-to-br from-green-50 to-emerald-50 p-6 rounded-xl border border-green-200">
                        <div className="flex items-center space-x-2 mb-4">
                          <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                            <Phone size={16} className="text-white" />
                          </div>
                          <h3 className="text-lg font-bold text-gray-900">Thông tin liên hệ</h3>
                        </div>
                        <div className="space-y-4">
                          {selectedLeader.phone && (
                            <div className="bg-white p-4 rounded-lg border border-green-100">
                              <div className="flex items-center space-x-3">
                                <Phone size={18} className="text-green-600" />
                                <div>
                                  <p className="text-sm text-gray-500 font-medium">Điện thoại</p>
                                  <a href={`tel:${selectedLeader.phone}`} className="font-bold text-green-600 hover:text-green-700 transition-colors">
                                    {selectedLeader.phone}
                                  </a>
                                </div>
                              </div>
                            </div>
                          )}
                          
                          {selectedLeader.email && (
                            <div className="bg-white p-4 rounded-lg border border-[#e4393c] border-opacity-20">
                              <div className="flex items-center space-x-3">
                                <Mail size={18} className="text-[#e4393c]" />
                                <div>
                                  <p className="text-sm text-gray-500 font-medium">Email</p>
                                  <a href={`mailto:${selectedLeader.email}`} className="font-bold text-[#e4393c] hover:text-red-700 transition-colors break-all">
                                    {selectedLeader.email}
                                  </a>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>          </div>
        </div>
      )}
    </div>
  );
};

export default CourtLeadersPage;
