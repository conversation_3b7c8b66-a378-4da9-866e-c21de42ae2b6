"use client";
import { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import {
  Users,
  Phone,
  Mail,
  Award,
  BookOpen,
  Clock,
  Shield,
  User,
  ChevronLeft,
  ChevronRight,
  Search,
  Filter
} from "react-feather";
import { getLeaderImageUrl } from "@/utils/imageUtils";

interface Leader {
  id: string;
  name: string;
  position: string;
  department: string;
  bio?: string;
  experience?: string;
  education?: string;
  achievements: string[];
  phone?: string;
  email?: string;
  image?: string;
  order: number;
  isActive: boolean;
}

interface PositionCategory {
  title: string;
  leaders: Leader[];
  color: string;
  icon: React.ReactNode;
}

const CourtLeadersPage = () => {
  const [leaders, setLeaders] = useState<Leader[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedDepartment, setSelectedDepartment] = useState<string>("all");
  const [selectedPosition, setSelectedPosition] = useState<string>("all");
  const [selectedLeader, setSelectedLeader] = useState<Leader | null>(null);
  const [showModal, setShowModal] = useState(false);
  useEffect(() => {
    fetchLeaders();
    
    // Add entrance animations
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add('animate-fade-in-up');
          }
        });
      },
      { threshold: 0.1 }
    );

    // Add CSS for animations
    if (typeof document !== 'undefined') {
      const style = document.createElement('style');
      style.textContent = `
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        .animate-fade-in-up {
          animation: fadeInUp 0.6s ease-out forwards;
        }
        .leader-card {
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .leader-card:hover {
          transform: translateY(-8px) scale(1.02);
        }
        .pulse-ring {
          animation: pulse-ring 2s cubic-bezier(0.455, 0.03, 0.515, 0.955) infinite;
        }
        @keyframes pulse-ring {
          0% {
            transform: scale(0.8);
            opacity: 1;
          }
          80%, 100% {
            transform: scale(1.2);
            opacity: 0;
          }
        }
        .floating {
          animation: floating 3s ease-in-out infinite;
        }
        @keyframes floating {
          0%, 100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-10px);
          }
        }

        /* Kim tự tháp styles */
        .pyramid-level-1 {
          background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
          border: 3px solid #dc2626;
        }
        .pyramid-level-2 {
          background: linear-gradient(135deg, #fed7aa 0%, #fdba74 100%);
          border: 2px solid #ea580c;
        }
        .pyramid-level-3 {
          background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
          border: 2px solid #16a34a;
        }
        .pyramid-level-4 {
          background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
          border: 2px solid #059669;
        }
        .pyramid-level-5 {
          background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
          border: 2px solid #2563eb;
        }
        .pyramid-level-6 {
          background: linear-gradient(135deg, #e9d5ff 0%, #d8b4fe 100%);
          border: 2px solid #9333ea;
        }
        .pyramid-level-7 {
          background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
          border: 2px solid #6b7280;
        }
      `;
      document.head.appendChild(style);
    }

    return () => observer.disconnect();
  }, []);  const fetchLeaders = async () => {
    try {
      setLoading(true);
      
      const response = await fetch("/api/leaders/public");
      const result = await response.json();
      
      if (response.ok) {
        setLeaders(result.data || []);
      } else {
        console.error("Error fetching leaders:", result.message);
        setLeaders([]);
      }
    } catch (error) {
      console.error("Error fetching leaders:", error);
      setLeaders([]);
    } finally {
      setLoading(false);
    }
  };
  const getPositionIcon = (position: string) => {
    if (position.includes("Chánh án")) return <Shield size={24} className="text-red-600" />;
    if (position.includes("Phó")) return <Award size={24} className="text-[#e4393c]" />;
    return <User size={24} className="text-gray-600" />;
  };
  const getPositionColor = (position: string) => {
    if (position.includes("Chánh án")) return "bg-red-100 text-red-800 border-red-200";
    if (position.includes("Phó")) return "bg-[#e4393c] bg-opacity-10 text-[#e4393c] border-red-200";
    if (position.includes("Thẩm phán")) return "bg-green-100 text-green-800 border-green-200";
    return "bg-gray-100 text-gray-800 border-gray-200";
  };

  const filteredLeaders = leaders.filter(leader => {
    const matchesSearch = leader.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         leader.position.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         leader.department.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesDepartment = selectedDepartment === "all" || leader.department === selectedDepartment;
    const matchesPosition = selectedPosition === "all" || leader.position === selectedPosition;
    
    return matchesSearch && matchesDepartment && matchesPosition && leader.isActive;
  });

  // Định nghĩa thứ tự ưu tiên chức vụ (kim tự tháp từ cao xuống thấp)
  const getPositionPriority = (position: string): number => {
    if (position.includes("Chánh án") && !position.includes("Phó")) return 1; // Chánh án
    if (position.includes("Phó Chánh án")) return 2; // Phó Chánh án
    if (position.includes("Thẩm phán") && !position.includes("Phó")) return 3; // Thẩm phán
    if (position.includes("Phó Thẩm phán")) return 4; // Phó Thẩm phán
    if (position.includes("Thẩm tra viên")) return 5; // Thẩm tra viên
    if (position.includes("Thư ký")) return 6; // Thư ký Tòa án
    return 7; // Cán bộ khác
  };

  const groupedLeaders: PositionCategory[] = [
    {
      title: "Chánh án",
      leaders: filteredLeaders.filter(l => l.position.includes("Chánh án") && !l.position.includes("Phó")),
      color: "border-red-200 bg-gradient-to-br from-red-50 to-red-100",
      icon: <Shield size={32} className="text-red-600" />
    },
    {
      title: "Phó Chánh án",
      leaders: filteredLeaders.filter(l => l.position.includes("Phó Chánh án")),
      color: "border-orange-200 bg-gradient-to-br from-orange-50 to-orange-100",
      icon: <Shield size={32} className="text-orange-600" />
    },
    {
      title: "Thẩm phán",
      leaders: filteredLeaders.filter(l => l.position.includes("Thẩm phán") && !l.position.includes("Phó")),
      color: "border-green-200 bg-gradient-to-br from-green-50 to-green-100",
      icon: <Award size={32} className="text-green-600" />
    },
    {
      title: "Phó Thẩm phán",
      leaders: filteredLeaders.filter(l => l.position.includes("Phó Thẩm phán")),
      color: "border-emerald-200 bg-gradient-to-br from-emerald-50 to-emerald-100",
      icon: <Award size={32} className="text-emerald-600" />
    },
    {
      title: "Thẩm tra viên",
      leaders: filteredLeaders.filter(l => l.position.includes("Thẩm tra viên")),
      color: "border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100",
      icon: <BookOpen size={32} className="text-blue-600" />
    },
    {
      title: "Thư ký Tòa án",
      leaders: filteredLeaders.filter(l => l.position.includes("Thư ký")),
      color: "border-purple-200 bg-gradient-to-br from-purple-50 to-purple-100",
      icon: <User size={32} className="text-purple-600" />
    },
    {
      title: "Cán bộ khác",
      leaders: filteredLeaders.filter(l =>
        !l.position.includes("Chánh án") &&
        !l.position.includes("Thẩm phán") &&
        !l.position.includes("Thẩm tra viên") &&
        !l.position.includes("Thư ký")
      ),
      color: "border-gray-200 bg-gradient-to-br from-gray-50 to-gray-100",
      icon: <Users size={32} className="text-gray-600" />
    }
  ];

  const departments = [...new Set(leaders.map(l => l.department))];
  const positions = [...new Set(leaders.map(l => l.position))];

  const openModal = (leader: Leader) => {
    setSelectedLeader(leader);
    setShowModal(true);
    document.body.style.overflow = 'hidden';
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedLeader(null);
    document.body.style.overflow = 'unset';
  };  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-10"><div className="bg-white rounded-xl shadow-lg border-0 p-6 mb-8 backdrop-blur-sm">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Tìm kiếm lãnh đạo</h2>
              <p className="text-gray-600">Sử dụng bộ lọc để tìm kiếm thông tin lãnh đạo</p>
            </div>            <div className="hidden md:flex items-center space-x-2 text-sm text-gray-500">
              <span className="bg-[#e4393c] bg-opacity-10 text-[#e4393c] px-3 py-1 rounded-full font-medium">
                {filteredLeaders.length} kết quả
              </span>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Tìm kiếm theo tên, chức vụ..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#e4393c] focus:border-transparent transition-all"
              />
            </div>
            
            <div className="relative">
              <Filter size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />              <select
                value={selectedDepartment}
                onChange={(e) => setSelectedDepartment(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#e4393c] focus:border-transparent appearance-none transition-all"
              >
                <option value="all">Tất cả phòng ban</option>
                {departments.map(dept => (
                  <option key={dept} value={dept}>{dept}</option>
                ))}
              </select>
            </div>
            
            <div className="relative">
              <Filter size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />              <select
                value={selectedPosition}
                onChange={(e) => setSelectedPosition(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-[#e4393c] focus:border-transparent appearance-none transition-all"
              >
                <option value="all">Tất cả chức vụ</option>
                {positions.map(pos => (
                  <option key={pos} value={pos}>{pos}</option>
                ))}
              </select>
            </div>
          </div>
          
          {(searchTerm || selectedDepartment !== "all" || selectedPosition !== "all") && (
            <div className="mt-6 pt-4 border-t border-gray-100">
              <div className="flex items-center justify-between">                <p className="text-sm text-gray-600">
                  Tìm thấy <span className="font-semibold text-[#e4393c]">{filteredLeaders.length}</span> kết quả
                  {searchTerm && (
                    <span> cho "<strong className="text-gray-900">{searchTerm}</strong>"</span>
                  )}
                </p>
                <button
                  onClick={() => {
                    setSearchTerm("");
                    setSelectedDepartment("all");
                    setSelectedPosition("all");
                  }}
                  className="text-sm text-[#e4393c] hover:text-red-800 font-medium transition-colors"
                >
                  Xóa bộ lọc
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Leaders by Categories */}
        {loading ? (
          <div className="space-y-8">
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="bg-white rounded-lg shadow-sm border p-6">
                <div className="h-6 bg-gray-200 rounded w-1/4 mb-6 animate-pulse"></div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <div key={i} className="animate-pulse">
                      <div className="flex items-center space-x-4 p-4 border rounded-lg">
                        <div className="w-16 h-16 bg-gray-200 rounded-lg"></div>
                        <div className="flex-1">
                          <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                          <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
                          <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        ) : filteredLeaders.length === 0 ? (            <div className="bg-white rounded-xl shadow-lg border p-12 text-center">
              <div className="bg-gray-50 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
                <Users size={40} className="text-gray-400" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Không tìm thấy kết quả
              </h3>
              <p className="text-gray-500 mb-8 max-w-md mx-auto">
                Thử thay đổi từ khóa tìm kiếm hoặc bộ lọc để xem thêm kết quả phù hợp
              </p>              <button
                onClick={() => {
                  setSearchTerm("");
                  setSelectedDepartment("all");
                  setSelectedPosition("all");
                }}
                className="bg-[#e4393c] hover:bg-red-700 text-white px-8 py-3 rounded-xl transition-all transform hover:scale-105 font-medium shadow-lg"
              >
                Xóa tất cả bộ lọc
              </button>
            </div>
        ) : (
          <div className="space-y-8">
            {groupedLeaders.filter(category => category.leaders.length > 0).map((category, index) => {
              // Tính toán kích thước và margin dựa trên thứ bậc (kim tự tháp)
              const getHierarchyStyle = (categoryTitle: string, categoryIndex: number) => {
                if (categoryTitle.includes("Chánh án") && !categoryTitle.includes("Phó"))
                  return "scale-105 mx-0 shadow-2xl pyramid-level-1"; // Lớn nhất, không margin
                if (categoryTitle.includes("Phó Chánh án"))
                  return "scale-102 mx-4 shadow-xl pyramid-level-2";
                if (categoryTitle.includes("Thẩm phán") && !categoryTitle.includes("Phó"))
                  return "scale-100 mx-8 shadow-lg pyramid-level-3";
                if (categoryTitle.includes("Phó Thẩm phán"))
                  return "scale-98 mx-12 shadow-lg pyramid-level-4";
                if (categoryTitle.includes("Thẩm tra viên"))
                  return "scale-96 mx-16 shadow-md pyramid-level-5";
                if (categoryTitle.includes("Thư ký"))
                  return "scale-94 mx-20 shadow-md pyramid-level-6";
                return "scale-92 mx-24 shadow pyramid-level-7"; // Nhỏ nhất, margin lớn nhất
              };

              return (
                <div key={category.title} className={`bg-white rounded-xl border-0 overflow-hidden transform transition-all hover:shadow-xl ${getHierarchyStyle(category.title, index)}`}>
                <div className={`p-6 ${category.color} border-b-4 border-opacity-20 relative`}>
                  {/* Thêm indicator cấp bậc kim tự tháp */}
                  <div className="absolute top-2 right-2">
                    <div className="flex space-x-1">
                      {Array.from({ length: Math.max(1, 8 - index) }, (_, i) => (
                        <div key={i} className="w-2 h-2 bg-white/40 rounded-full"></div>
                      ))}
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="bg-white/20 backdrop-blur-sm rounded-xl p-3 shadow-lg">
                        {category.icon}
                      </div>
                      <div>
                        <h2 className="text-2xl font-bold text-gray-900">
                          {category.title}
                        </h2>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="bg-white/50 backdrop-blur-sm rounded-lg px-4 py-2 shadow-md">
                        <span className="text-2xl font-bold text-gray-900">
                          {category.leaders.length}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {category.leaders
                      .sort((a, b) => {
                        // Sắp xếp theo thứ tự ưu tiên chức vụ trước, sau đó theo order
                        const priorityDiff = getPositionPriority(a.position) - getPositionPriority(b.position);
                        if (priorityDiff !== 0) return priorityDiff;
                        return a.order - b.order;
                      })
                      .map((leader, leaderIndex) => (
                        <div
                          key={leader.id}
                          className="bg-gradient-to-br from-white to-gray-50 border border-gray-200 rounded-xl p-6 hover:shadow-xl hover:border-[#e4393c] transition-all duration-300 cursor-pointer group transform hover:-translate-y-1 leader-card"
                          onClick={() => openModal(leader)}
                          style={{
                            animationDelay: `${leaderIndex * 100}ms`
                          }}
                        >
                          <div className="flex items-start space-x-4 mb-4">                            <div className="relative">
                              <div className="w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl flex items-center justify-center overflow-hidden flex-shrink-0 ring-2 ring-white shadow-lg">
                                {leader.image ? (
                                  <img
                                    src={getLeaderImageUrl(leader.image)}
                                    alt={leader.name}
                                    className="w-full h-full object-cover"
                                  />
                                ) : (
                                  <User size={24} className="text-gray-400" />
                                )}
                              </div>
                            </div>
                            <div className="flex-1 min-w-0">                              <h3 className="font-bold text-gray-900 group-hover:text-[#e4393c] transition-colors text-lg mb-1">
                                {leader.name}
                              </h3>
                              <div className="flex items-center space-x-2 mb-2">
                                <div className="w-4 h-4 flex items-center justify-center">
                                  {getPositionIcon(leader.position)}
                                </div>
                                <span className={`px-3 py-1 rounded-full text-xs font-semibold border ${getPositionColor(leader.position)}`}>
                                  {leader.position}
                                </span>
                              </div>
                              <p className="text-sm font-medium text-gray-600 bg-gray-100 px-2 py-1 rounded-lg inline-block">
                                {leader.department}
                              </p>
                            </div>
                          </div>                            <div className="space-y-3 text-sm text-gray-600 mb-4">
                              {leader.experience && (
                                <div className="flex items-center space-x-3 bg-red-50 p-2 rounded-lg">
                                  <Clock size={14} className="text-[#e4393c]" />
                                  <span className="font-medium">{leader.experience} kinh nghiệm</span>
                                </div>
                              )}
                            {leader.education && (
                              <div className="flex items-center space-x-3 bg-green-50 p-2 rounded-lg">
                                <BookOpen size={14} className="text-green-600" />
                                <span className="line-clamp-1 font-medium">{leader.education}</span>
                              </div>
                            )}
                          </div>
                          
                          {leader.bio && (
                            <p className="text-sm text-gray-700 mb-4 line-clamp-2 leading-relaxed">
                              {leader.bio}
                            </p>
                          )}
                          
                          <div className="pt-4 border-t border-gray-100 flex items-center justify-between">
                            <div className="flex items-center space-x-2 text-xs text-gray-500">
                              <Award size={12} className="text-yellow-600" />
                              <span className="font-medium">{leader.achievements.length} thành tích</span>
                            </div>                            <div className="flex items-center space-x-2 text-[#e4393c] group-hover:text-red-700 transition-colors">
                              <span className="text-xs font-medium">Xem chi tiết</span>
                              <ChevronRight size={14} className="transform group-hover:translate-x-1 transition-transform" />
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              </div>
              );
            })}
          </div>
        )}
      </div>      {/* Modal */}
      {showModal && selectedLeader && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div
              className="fixed inset-0 transition-opacity bg-black bg-opacity-60 backdrop-blur-sm"
              onClick={closeModal}
            ></div>

            <div className="inline-block w-full max-w-5xl my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-2xl rounded-2xl">              {/* Modal Header */}
              <div className="bg-gradient-to-br from-[#e4393c] via-red-600 to-red-800 px-8 py-10 text-white relative overflow-hidden">
                <div className="absolute inset-0 bg-black/10"></div>
                <div className="absolute top-0 right-0 w-32 h-32 bg-white/5 rounded-full -mr-16 -mt-16"></div>
                <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/5 rounded-full -ml-12 -mb-12"></div>
                
                <div className="relative flex items-center justify-between">
                  <div className="flex items-center space-x-6">                    <div className="relative">
                      <div className="w-24 h-24 bg-white/10 backdrop-blur-sm rounded-2xl flex items-center justify-center overflow-hidden ring-4 ring-white/20">
                        {selectedLeader.image ? (
                          <img
                            src={getLeaderImageUrl(selectedLeader.image)}
                            alt={selectedLeader.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <User size={40} className="text-white" />
                        )}
                      </div>
                    </div>
                    <div>
                      <h2 className="text-3xl font-bold mb-2">{selectedLeader.name}</h2>
                      <div className="flex items-center space-x-3 mb-2">
                        <div className="w-6 h-6 flex items-center justify-center">
                          {getPositionIcon(selectedLeader.position)}
                        </div>
                        <span className="bg-white/20 backdrop-blur-sm px-4 py-2 rounded-xl text-sm font-semibold">
                          {selectedLeader.position}
                        </span>
                      </div>
                      <p className="text-blue-100 text-lg font-medium">{selectedLeader.department}</p>
                    </div>
                  </div>                  <button
                    onClick={closeModal}
                    className="text-white hover:text-red-200 transition-colors bg-white/10 backdrop-blur-sm rounded-xl p-3 hover:bg-white/20"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>

              {/* Modal Content */}
              <div className="px-8 py-8 max-h-96 overflow-y-auto">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  {/* Main Info */}
                  <div className="lg:col-span-2 space-y-8">
                    <div>
                      <div className="flex items-center space-x-2 mb-4">                        <div className="w-8 h-8 bg-[#e4393c] bg-opacity-10 rounded-lg flex items-center justify-center">
                          <User size={16} className="text-[#e4393c]" />
                        </div>
                        <h3 className="text-xl font-bold text-gray-900">Tiểu sử</h3>
                      </div>
                      <div className="bg-gray-50 rounded-xl p-6">
                        <p className="text-gray-700 leading-relaxed text-base">
                          {selectedLeader.bio || "Chưa có thông tin tiểu sử"}
                        </p>
                      </div>
                    </div>

                    {selectedLeader.achievements.length > 0 && (
                      <div>
                        <div className="flex items-center space-x-2 mb-4">                        <div className="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                          <Award size={16} className="text-yellow-600" />
                        </div>
                          <h3 className="text-xl font-bold text-gray-900">Thành tích & Giải thưởng</h3>
                        </div>
                        <div className="space-y-3">
                          {selectedLeader.achievements.map((achievement, index) => (
                            <div key={index} className="flex items-start space-x-4 bg-gradient-to-r from-yellow-50 to-orange-50 p-4 rounded-xl border border-yellow-200">
                              <div className="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5">
                                <Award size={16} className="text-white" />
                              </div>
                              <div>
                                <span className="text-gray-800 font-medium">{achievement}</span>
                                <div className="text-xs text-yellow-700 mt-1">Thành tích xuất sắc</div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Side Info */}
                  <div className="space-y-6">                    {(selectedLeader.experience || selectedLeader.education || selectedLeader.bio) && (
                      <div className="bg-gradient-to-br from-red-50 to-pink-50 p-6 rounded-xl border border-red-200">
                        <div className="flex items-center space-x-2 mb-4">
                          <div className="w-8 h-8 bg-[#e4393c] rounded-lg flex items-center justify-center">
                            <Clock size={16} className="text-white" />
                          </div>
                          <h3 className="text-lg font-bold text-gray-900">Thông tin chi tiết</h3>
                        </div>
                        <div className="space-y-4">
                          {selectedLeader.experience && (
                            <div className="bg-white p-4 rounded-lg border border-red-100">
                              <div className="flex items-center space-x-3">
                                <Clock size={18} className="text-[#e4393c]" />
                                <div>
                                  <p className="text-sm text-gray-500 font-medium">Kinh nghiệm</p>
                                  <p className="font-bold text-gray-900">{selectedLeader.experience}</p>
                                </div>
                              </div>
                            </div>
                          )}

                          {selectedLeader.education && (
                            <div className="bg-white p-4 rounded-lg border border-green-100">
                              <div className="flex items-center space-x-3">
                                <BookOpen size={18} className="text-green-600" />
                                <div>
                                  <p className="text-sm text-gray-500 font-medium">Học vấn</p>
                                  <p className="font-bold text-gray-900">{selectedLeader.education}</p>
                                </div>
                              </div>
                            </div>
                          )}

                          {selectedLeader.bio && (
                            <div className="bg-white p-4 rounded-lg border border-blue-100">
                              <div className="flex items-center space-x-3">
                                <User size={18} className="text-blue-600" />
                                <div>
                                  <p className="text-sm text-gray-500 font-medium">Tiểu sử</p>
                                  <p className="font-bold text-gray-900">{selectedLeader.bio}</p>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {(selectedLeader.phone || selectedLeader.email) && (                      <div className="bg-gradient-to-br from-green-50 to-emerald-50 p-6 rounded-xl border border-green-200">
                        <div className="flex items-center space-x-2 mb-4">
                          <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                            <Phone size={16} className="text-white" />
                          </div>
                          <h3 className="text-lg font-bold text-gray-900">Thông tin liên hệ</h3>
                        </div>
                        <div className="space-y-4">
                          {selectedLeader.phone && (
                            <div className="bg-white p-4 rounded-lg border border-green-100">
                              <div className="flex items-center space-x-3">
                                <Phone size={18} className="text-green-600" />
                                <div>
                                  <p className="text-sm text-gray-500 font-medium">Điện thoại</p>
                                  <a href={`tel:${selectedLeader.phone}`} className="font-bold text-green-600 hover:text-green-700 transition-colors">
                                    {selectedLeader.phone}
                                  </a>
                                </div>
                              </div>
                            </div>
                          )}
                          
                          {selectedLeader.email && (
                            <div className="bg-white p-4 rounded-lg border border-[#e4393c] border-opacity-20">
                              <div className="flex items-center space-x-3">
                                <Mail size={18} className="text-[#e4393c]" />
                                <div>
                                  <p className="text-sm text-gray-500 font-medium">Email</p>
                                  <a href={`mailto:${selectedLeader.email}`} className="font-bold text-[#e4393c] hover:text-red-700 transition-colors break-all">
                                    {selectedLeader.email}
                                  </a>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>          </div>
        </div>
      )}
    </div>
  );
};

export default CourtLeadersPage;
