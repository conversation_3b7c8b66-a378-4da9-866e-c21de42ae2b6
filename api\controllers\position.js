const Position = require("../models/position");
const User = require("../models/user");

// Lấy tất cả positions
exports.getAllPositions = async (req, res) => {
  try {
    const positions = await Position.find({ isActive: true })
      .populate("createdBy", "username")
      .sort({ order: 1, level: -1 });
    
    res.status(200).json({
      success: true,
      positions
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      message: "Không thể lấy danh sách chức vụ",
      error: err.message
    });
  }
};

// Lấy position theo ID
exports.getPositionById = async (req, res) => {
  try {
    const { id } = req.params;
    const position = await Position.findById(id).populate("createdBy", "username");
    
    if (!position) {
      return res.status(404).json({
        success: false,
        message: "<PERSON>hông tìm thấy chức vụ"
      });
    }
    
    res.status(200).json({
      success: true,
      position
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      message: "<PERSON>h<PERSON>ng thể lấy thông tin chức vụ",
      error: err.message
    });
  }
};

// Tạo position mới
exports.createPosition = async (req, res) => {
  try {
    const { name, description, permissions, level, order } = req.body;
    
    // Kiểm tra tên chức vụ đã tồn tại chưa
    const existingPosition = await Position.findOne({ 
      name: { $regex: new RegExp(`^${name}$`, 'i') }
    });
    if (existingPosition) {
      return res.status(400).json({
        success: false,
        message: "Tên chức vụ đã tồn tại"
      });
    }
    
    const position = new Position({
      name: name.trim(),
      description: description?.trim() || "",
      permissions: permissions || ["read"],
      level: level || 1,
      order: order || 0,
      createdBy: req.user._id
    });
    
    await position.save();
    
    res.status(201).json({
      success: true,
      message: "Tạo chức vụ thành công",
      position
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      message: "Không thể tạo chức vụ",
      error: err.message
    });
  }
};

// Cập nhật position
exports.updatePosition = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, permissions, level, order, isActive } = req.body;
    
    const position = await Position.findById(id);
    if (!position) {
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy chức vụ"
      });
    }
    
    // Không cho phép sửa position mặc định
    if (position.isDefault && (name || level)) {
      return res.status(403).json({
        success: false,
        message: "Không thể sửa tên và cấp độ của chức vụ mặc định"
      });
    }
    
    // Kiểm tra tên mới đã tồn tại chưa (nếu thay đổi tên)
    if (name && name.toLowerCase() !== position.name.toLowerCase()) {
      const existingPosition = await Position.findOne({ 
        name: { $regex: new RegExp(`^${name}$`, 'i') },
        _id: { $ne: id }
      });
      if (existingPosition) {
        return res.status(400).json({
          success: false,
          message: "Tên chức vụ đã tồn tại"
        });
      }
    }
    
    const updateData = {};
    if (name) updateData.name = name.trim();
    if (description !== undefined) updateData.description = description.trim();
    if (permissions) updateData.permissions = permissions;
    if (level !== undefined) updateData.level = level;
    if (order !== undefined) updateData.order = order;
    if (isActive !== undefined) updateData.isActive = isActive;
    
    const updatedPosition = await Position.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    ).populate("createdBy", "username");
    
    res.status(200).json({
      success: true,
      message: "Cập nhật chức vụ thành công",
      position: updatedPosition
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      message: "Không thể cập nhật chức vụ",
      error: err.message
    });
  }
};

// Xóa position
exports.deletePosition = async (req, res) => {
  try {
    const { id } = req.params;
    
    const position = await Position.findById(id);
    if (!position) {
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy chức vụ"
      });
    }
    
    // Không cho phép xóa position mặc định
    if (position.isDefault) {
      return res.status(403).json({
        success: false,
        message: "Không thể xóa chức vụ mặc định"
      });
    }
    
    // Kiểm tra xem có user nào đang sử dụng position này không
    const usersWithPosition = await User.countDocuments({ 
      $or: [
        { rule: position.name },
        { position: id }
      ]
    });
    
    if (usersWithPosition > 0) {
      return res.status(400).json({
        success: false,
        message: `Không thể xóa chức vụ này vì có ${usersWithPosition} người dùng đang sử dụng`
      });
    }
    
    await Position.findByIdAndDelete(id);
    
    res.status(200).json({
      success: true,
      message: "Xóa chức vụ thành công"
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      message: "Không thể xóa chức vụ",
      error: err.message
    });
  }
};

// Lấy danh sách permissions có sẵn
exports.getAvailablePermissions = async (req, res) => {
  try {
    const permissions = [
      { key: "create", name: "Tạo mới", description: "Quyền tạo nội dung mới" },
      { key: "read", name: "Đọc", description: "Quyền xem nội dung" },
      { key: "update", name: "Cập nhật", description: "Quyền chỉnh sửa nội dung" },
      { key: "delete", name: "Xóa", description: "Quyền xóa nội dung" },
      { key: "manage_users", name: "Quản lý người dùng", description: "Quyền quản lý tài khoản người dùng" },
      { key: "manage_categories", name: "Quản lý danh mục", description: "Quyền quản lý danh mục bài viết" },
      { key: "manage_posts", name: "Quản lý bài viết", description: "Quyền quản lý bài viết của người khác" },
      { key: "view_analytics", name: "Xem báo cáo", description: "Quyền xem báo cáo thống kê" }
    ];
    
    res.status(200).json({
      success: true,
      permissions
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      message: "Không thể lấy danh sách quyền",
      error: err.message
    });
  }
};

// Cập nhật thứ tự positions
exports.updatePositionsOrder = async (req, res) => {
  try {
    const { positions } = req.body; // Array of {id, order}
    
    const updatePromises = positions.map(item => 
      Position.findByIdAndUpdate(item.id, { order: item.order })
    );
    
    await Promise.all(updatePromises);
    
    res.status(200).json({
      success: true,
      message: "Cập nhật thứ tự thành công"
    });
  } catch (err) {
    res.status(500).json({
      success: false,
      message: "Không thể cập nhật thứ tự",
      error: err.message
    });
  }
};
