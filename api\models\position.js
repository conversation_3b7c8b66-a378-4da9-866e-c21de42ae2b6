const mongoose = require("mongoose");
const { Schema } = mongoose;

const positionSchema = new Schema(
  {
    name: { 
      type: String, 
      required: true,
      unique: true,
      trim: true
    },
    description: { 
      type: String, 
      default: "",
      trim: true
    },
    level: { 
      type: Number, 
      default: 1, // 1 = thấp nhất, số càng cao quyền càng lớn
      min: 1,
      max: 10
    },
    permissions: [{
      type: String,
      enum: ["create", "read", "update", "delete", "manage_users", "manage_categories", "manage_posts", "view_analytics"]
    }],
    isActive: { 
      type: Boolean, 
      default: true 
    },
    isDefault: { 
      type: <PERSON><PERSON>an, 
      default: false // Đánh dấu position mặc định không thể xóa
    },
    order: {
      type: Number,
      default: 0 // Thứ tự hiển thị
    },
    createdBy: { 
      type: Schema.Types.ObjectId, 
      ref: "User" 
    }
  },
  { timestamps: true }
);

// Index
positionSchema.index({ name: 1 });
positionSchema.index({ level: 1 });
positionSchema.index({ order: 1 });

// Static method để tạo positions mặc định
positionSchema.statics.createDefaultPositions = async function() {
  const defaultPositions = [
    {
      name: "admin",
      description: "Quản trị viên hệ thống",
      level: 10,
      permissions: ["create", "read", "update", "delete", "manage_users", "manage_categories", "manage_posts", "view_analytics"],
      isDefault: true,
      order: 1
    },
    {
      name: "manager", 
      description: "Quản lý",
      level: 8,
      permissions: ["create", "read", "update", "delete", "manage_categories", "manage_posts", "view_analytics"],
      isDefault: true,
      order: 2
    },
    {
      name: "editor",
      description: "Biên tập viên", 
      level: 5,
      permissions: ["create", "read", "update", "manage_posts"],
      isDefault: true,
      order: 3
    },
    {
      name: "user",
      description: "Người dùng",
      level: 1,
      permissions: ["read"],
      isDefault: true,
      order: 4
    }
  ];

  for (const position of defaultPositions) {
    const exists = await this.findOne({ name: position.name });
    if (!exists) {
      await this.create(position);
    }
  }
};

module.exports = mongoose.model("Position", positionSchema);
